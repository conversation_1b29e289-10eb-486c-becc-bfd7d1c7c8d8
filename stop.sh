#!/bin/bash

# Document Converter 停止脚本
# 用法:
#   ./stop.sh           # 正常停止
#   ./stop.sh force     # 强制停止

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_NAME="doc-converter"
PID_FILE="${SCRIPT_DIR}/${APP_NAME}.pid"
LOG_FILE="${SCRIPT_DIR}/logs/${APP_NAME}.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查应用状态
check_status() {
    if [ ! -f "$PID_FILE" ]; then
        log_info "$APP_NAME is not running (no PID file found)"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    if ! kill -0 "$pid" 2>/dev/null; then
        log_warn "$APP_NAME is not running (PID $pid not found)"
        rm -f "$PID_FILE"
        return 1
    fi
    
    log_debug "$APP_NAME is running (PID: $pid)"
    return 0
}

# 正常停止
stop_gracefully() {
    log_info "Stopping $APP_NAME gracefully..."
    
    if ! check_status; then
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    
    # 发送TERM信号
    log_debug "Sending TERM signal to PID $pid"
    kill -TERM "$pid"
    
    # 等待进程结束
    local count=0
    local max_wait=30
    
    while [ $count -lt $max_wait ]; do
        if ! kill -0 "$pid" 2>/dev/null; then
            log_info "$APP_NAME stopped successfully"
            rm -f "$PID_FILE"
            return 0
        fi
        
        sleep 1
        count=$((count + 1))
        
        if [ $((count % 5)) -eq 0 ]; then
            log_debug "Waiting for $APP_NAME to stop... ($count/$max_wait seconds)"
        fi
    done
    
    log_warn "$APP_NAME did not stop gracefully within $max_wait seconds"
    return 1
}

# 强制停止
stop_forcefully() {
    log_warn "Force stopping $APP_NAME..."
    
    if ! check_status; then
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    
    # 发送KILL信号
    log_debug "Sending KILL signal to PID $pid"
    kill -KILL "$pid" 2>/dev/null || true
    
    # 等待一下确认进程结束
    sleep 2
    
    if ! kill -0 "$pid" 2>/dev/null; then
        log_info "$APP_NAME force stopped successfully"
        rm -f "$PID_FILE"
        return 0
    else
        log_error "Failed to force stop $APP_NAME"
        return 1
    fi
}

# 停止所有相关进程
stop_all() {
    log_info "Stopping all $APP_NAME processes..."
    
    # 查找所有相关进程
    local pids=$(pgrep -f "doc-converter.*\.jar" || true)
    
    if [ -z "$pids" ]; then
        log_info "No $APP_NAME processes found"
        rm -f "$PID_FILE"
        return 0
    fi
    
    log_debug "Found processes: $pids"
    
    # 逐个停止
    for pid in $pids; do
        log_debug "Stopping process $pid"
        kill -TERM "$pid" 2>/dev/null || true
    done
    
    # 等待进程结束
    sleep 3
    
    # 检查是否还有进程运行
    local remaining=$(pgrep -f "doc-converter.*\.jar" || true)
    
    if [ -n "$remaining" ]; then
        log_warn "Some processes are still running, force killing..."
        for pid in $remaining; do
            kill -KILL "$pid" 2>/dev/null || true
        done
        sleep 1
    fi
    
    log_info "All $APP_NAME processes stopped"
    rm -f "$PID_FILE"
}

# 显示状态
show_status() {
    log_info "Checking $APP_NAME status..."
    
    if check_status; then
        local pid=$(cat "$PID_FILE")
        log_info "$APP_NAME is running (PID: $pid)"
        
        # 显示进程信息
        if command -v ps &> /dev/null; then
            log_debug "Process details:"
            ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || true
        fi
        
        # 显示端口信息
        if command -v netstat &> /dev/null; then
            log_debug "Listening ports:"
            netstat -tlnp 2>/dev/null | grep "$pid" || true
        elif command -v ss &> /dev/null; then
            log_debug "Listening ports:"
            ss -tlnp 2>/dev/null | grep "$pid" || true
        fi
        
        return 0
    else
        log_info "$APP_NAME is not running"
        return 1
    fi
}

# 显示帮助
show_help() {
    echo "Document Converter Stop Script"
    echo "=============================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  (no args)    Stop the application gracefully"
    echo "  force        Force stop the application"
    echo "  all          Stop all related processes"
    echo "  status       Show application status"
    echo "  help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0           # Normal stop"
    echo "  $0 force     # Force stop"
    echo "  $0 status    # Check status"
}

# 主函数
main() {
    local command="${1:-stop}"
    
    case "$command" in
        "stop"|"")
            if ! stop_gracefully; then
                log_warn "Graceful stop failed, trying force stop..."
                stop_forcefully
            fi
            ;;
        "force")
            stop_forcefully
            ;;
        "all")
            stop_all
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
