#!/bin/bash

# Test script for MCP Task Management endpoints
BASE_URL="http://localhost:8080/doc-converter/mcp/tasks"

echo "=== Testing MCP Task Management Endpoints ==="
echo

# Test 1: List available tools
echo "1. Testing tools discovery..."
curl -X POST "$BASE_URL/tools/list" \
  -H "Content-Type: application/json" \
  -d '{}' \
  -s | jq '.' || echo "Failed to parse JSON"
echo
echo "---"

# Test 2: List tasks
echo "2. Testing list tasks..."
curl -X POST "$BASE_URL/tools/list_tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "arguments": {
      "page": 0,
      "size": 10
    }
  }' \
  -s | jq '.' || echo "Failed to parse JSON"
echo
echo "---"

# Test 3: Create a task
echo "3. Testing create task..."
TASK_RESPONSE=$(curl -X POST "$BASE_URL/tools/create_task" \
  -H "Content-Type: application/json" \
  -d '{
    "arguments": {
      "fileName": "test-document.pdf",
      "filePath": "/tmp/test-document.pdf",
      "fileType": "PDF",
      "options": {
        "quality": "high",
        "preserveFormatting": true
      }
    }
  }' \
  -s)

echo "$TASK_RESPONSE" | jq '.' || echo "Failed to parse JSON"

# Extract task ID for further tests
TASK_ID=$(echo "$TASK_RESPONSE" | jq -r '.content.task.taskId // empty')
echo "Created task ID: $TASK_ID"
echo
echo "---"

# Test 4: Get task status (if task was created)
if [ ! -z "$TASK_ID" ]; then
  echo "4. Testing get task status..."
  curl -X POST "$BASE_URL/tools/get_task_status" \
    -H "Content-Type: application/json" \
    -d "{
      \"arguments\": {
        \"taskId\": \"$TASK_ID\"
      }
    }" \
    -s | jq '.' || echo "Failed to parse JSON"
  echo
  echo "---"

  # Test 5: Update task progress
  echo "5. Testing update task progress..."
  curl -X POST "$BASE_URL/tools/update_task" \
    -H "Content-Type: application/json" \
    -d "{
      \"arguments\": {
        \"taskId\": \"$TASK_ID\",
        \"progress\": 50
      }
    }" \
    -s | jq '.' || echo "Failed to parse JSON"
  echo
  echo "---"

  # Test 6: Get task details
  echo "6. Testing get task..."
  curl -X POST "$BASE_URL/tools/get_task" \
    -H "Content-Type: application/json" \
    -d "{
      \"arguments\": {
        \"taskId\": \"$TASK_ID\"
      }
    }" \
    -s | jq '.' || echo "Failed to parse JSON"
  echo
  echo "---"

  # Test 7: Cancel task
  echo "7. Testing cancel task..."
  curl -X POST "$BASE_URL/tools/cancel_task" \
    -H "Content-Type: application/json" \
    -d "{
      \"arguments\": {
        \"taskId\": \"$TASK_ID\"
      }
    }" \
    -s | jq '.' || echo "Failed to parse JSON"
  echo
  echo "---"
fi

# Test 8: Test SSE events endpoint
echo "8. Testing SSE events endpoint (will timeout after 5 seconds)..."
timeout 5s curl -N "$BASE_URL/events?clientId=test-client" \
  -H "Accept: text/event-stream" \
  -s || echo "SSE test completed (timeout expected)"
echo
echo "---"

echo "=== MCP Testing Complete ==="
