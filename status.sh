#!/bin/bash

# Document Converter 状态检查脚本
# 用法:
#   ./status.sh         # 显示详细状态
#   ./status.sh simple  # 显示简单状态

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_NAME="doc-converter"
PID_FILE="${SCRIPT_DIR}/${APP_NAME}.pid"
LOG_FILE="${SCRIPT_DIR}/logs/${APP_NAME}.log"
JAR_FILE="target/${APP_NAME}-1.0.0-SNAPSHOT.jar"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

log_header() {
    echo -e "${CYAN}$1${NC}"
}

# 检查应用状态
check_app_status() {
    if [ ! -f "$PID_FILE" ]; then
        echo "STOPPED"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    if ! kill -0 "$pid" 2>/dev/null; then
        echo "STOPPED"
        return 1
    fi
    
    echo "RUNNING"
    return 0
}

# 获取进程信息
get_process_info() {
    if [ ! -f "$PID_FILE" ]; then
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    if ! kill -0 "$pid" 2>/dev/null; then
        return 1
    fi
    
    if command -v ps &> /dev/null; then
        ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem --no-headers 2>/dev/null
    fi
}

# 获取端口信息
get_port_info() {
    if [ ! -f "$PID_FILE" ]; then
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    if ! kill -0 "$pid" 2>/dev/null; then
        return 1
    fi
    
    if command -v netstat &> /dev/null; then
        netstat -tlnp 2>/dev/null | grep "$pid" | awk '{print $4}' | cut -d: -f2 | sort -n
    elif command -v ss &> /dev/null; then
        ss -tlnp 2>/dev/null | grep "$pid" | awk '{print $4}' | cut -d: -f2 | sort -n
    fi
}

# 检查服务健康状态
check_health() {
    local ports=$(get_port_info)
    
    for port in $ports; do
        if [ "$port" = "8080" ]; then
            if command -v curl &> /dev/null; then
                local response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$port/actuator/health" 2>/dev/null || echo "000")
                if [ "$response" = "200" ]; then
                    echo "HEALTHY"
                    return 0
                fi
            fi
        fi
    done
    
    echo "UNKNOWN"
    return 1
}

# 获取日志尾部
get_log_tail() {
    if [ -f "$LOG_FILE" ]; then
        tail -n 10 "$LOG_FILE" 2>/dev/null
    fi
}

# 获取系统资源使用情况
get_resource_usage() {
    if [ ! -f "$PID_FILE" ]; then
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    if ! kill -0 "$pid" 2>/dev/null; then
        return 1
    fi
    
    if command -v ps &> /dev/null; then
        ps -p "$pid" -o pcpu,pmem,vsz,rss --no-headers 2>/dev/null
    fi
}

# 简单状态显示
show_simple_status() {
    local status=$(check_app_status)
    local pid=""
    
    if [ -f "$PID_FILE" ] && [ "$status" = "RUNNING" ]; then
        pid=" (PID: $(cat "$PID_FILE"))"
    fi
    
    case "$status" in
        "RUNNING")
            log_info "$APP_NAME is $status$pid"
            ;;
        "STOPPED")
            log_warn "$APP_NAME is $status"
            ;;
        *)
            log_error "$APP_NAME status is $status"
            ;;
    esac
}

# 详细状态显示
show_detailed_status() {
    log_header "Document Converter Status Report"
    log_header "================================"
    echo ""
    
    # 应用状态
    log_header "Application Status:"
    local status=$(check_app_status)
    
    case "$status" in
        "RUNNING")
            local pid=$(cat "$PID_FILE")
            log_info "Status: $status (PID: $pid)"
            
            # 健康检查
            local health=$(check_health)
            log_info "Health: $health"
            
            # 进程信息
            log_header "Process Information:"
            local proc_info=$(get_process_info)
            if [ -n "$proc_info" ]; then
                echo "  PID   PPID  CMD                                    ELAPSED    CPU   MEM"
                echo "  $proc_info"
            fi
            
            # 资源使用
            log_header "Resource Usage:"
            local resource=$(get_resource_usage)
            if [ -n "$resource" ]; then
                echo "  CPU%   MEM%   VSZ(KB)   RSS(KB)"
                echo "  $resource"
            fi
            
            # 端口信息
            log_header "Listening Ports:"
            local ports=$(get_port_info)
            if [ -n "$ports" ]; then
                for port in $ports; do
                    echo "  Port $port"
                done
            else
                echo "  No ports found"
            fi
            ;;
        "STOPPED")
            log_warn "Status: $status"
            ;;
        *)
            log_error "Status: $status"
            ;;
    esac
    
    echo ""
    
    # 文件信息
    log_header "File Information:"
    if [ -f "$JAR_FILE" ]; then
        local jar_size=$(ls -lh "$JAR_FILE" | awk '{print $5}')
        local jar_date=$(ls -l "$JAR_FILE" | awk '{print $6, $7, $8}')
        log_info "JAR file: $JAR_FILE ($jar_size, $jar_date)"
    else
        log_warn "JAR file not found: $JAR_FILE"
    fi
    
    if [ -f "$PID_FILE" ]; then
        log_info "PID file: $PID_FILE"
    else
        log_debug "PID file not found: $PID_FILE"
    fi
    
    if [ -f "$LOG_FILE" ]; then
        local log_size=$(ls -lh "$LOG_FILE" | awk '{print $5}')
        log_info "Log file: $LOG_FILE ($log_size)"
    else
        log_debug "Log file not found: $LOG_FILE"
    fi
    
    echo ""
    
    # 最近日志
    if [ -f "$LOG_FILE" ] && [ "$status" = "RUNNING" ]; then
        log_header "Recent Log Entries (last 10 lines):"
        get_log_tail | while IFS= read -r line; do
            echo "  $line"
        done
    fi
    
    echo ""
    
    # 系统信息
    log_header "System Information:"
    if command -v java &> /dev/null; then
        local java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2)
        log_info "Java version: $java_version"
    fi
    
    if command -v free &> /dev/null; then
        local mem_info=$(free -h | grep "Mem:" | awk '{print "Total: " $2 ", Used: " $3 ", Free: " $4}')
        log_info "Memory: $mem_info"
    fi
    
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | xargs)
    log_info "Load average: $load_avg"
}

# 显示帮助
show_help() {
    echo "Document Converter Status Script"
    echo "==============================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  (no args)    Show detailed status"
    echo "  simple       Show simple status"
    echo "  health       Check health status only"
    echo "  logs         Show recent log entries"
    echo "  help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0           # Detailed status"
    echo "  $0 simple    # Simple status"
    echo "  $0 health    # Health check"
}

# 主函数
main() {
    local command="${1:-detailed}"
    
    case "$command" in
        "detailed"|"")
            show_detailed_status
            ;;
        "simple")
            show_simple_status
            ;;
        "health")
            local health=$(check_health)
            echo "Health: $health"
            [ "$health" = "HEALTHY" ] && exit 0 || exit 1
            ;;
        "logs")
            if [ -f "$LOG_FILE" ]; then
                log_header "Recent Log Entries:"
                get_log_tail
            else
                log_warn "Log file not found: $LOG_FILE"
            fi
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
