package com.talkweb.ai.converter.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;

import com.talkweb.ai.converter.web.service.ConversionTaskService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Test class for MCP Task Controller
 */
@WebMvcTest(McpTaskController.class)
@ActiveProfiles("server")
class McpTaskControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ConversionTaskService taskService;

    @Test
    void testListToolsEndpoint() throws Exception {
        mockMvc.perform(post("/mcp/tasks/tools/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.tools").exists())
                .andExpect(jsonPath("$.tools.list_tasks").exists())
                .andExpect(jsonPath("$.tools.get_task").exists())
                .andExpect(jsonPath("$.tools.create_task").exists())
                .andExpect(jsonPath("$.tools.update_task").exists())
                .andExpect(jsonPath("$.tools.delete_task").exists())
                .andExpect(jsonPath("$.tools.get_task_status").exists())
                .andExpect(jsonPath("$.tools.cancel_task").exists());
    }

    @Test
    void testCreateTaskTool() throws Exception {
        Map<String, Object> request = Map.of(
            "arguments", Map.of(
                "fileName", "test.pdf",
                "filePath", "/tmp/test.pdf",
                "fileType", "PDF"
            )
        );

        mockMvc.perform(post("/mcp/tasks/tools/create_task")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }

    @Test
    void testListTasksTool() throws Exception {
        Map<String, Object> request = Map.of(
            "arguments", Map.of(
                "page", 0,
                "size", 10
            )
        );

        mockMvc.perform(post("/mcp/tasks/tools/list_tasks")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }

    @Test
    void testGetTaskStatusTool() throws Exception {
        Map<String, Object> request = Map.of(
            "arguments", Map.of(
                "taskId", "test-task-id"
            )
        );

        mockMvc.perform(post("/mcp/tasks/tools/get_task_status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }

    @Test
    void testUpdateTaskTool() throws Exception {
        Map<String, Object> request = Map.of(
            "arguments", Map.of(
                "taskId", "test-task-id",
                "progress", 50
            )
        );

        mockMvc.perform(post("/mcp/tasks/tools/update_task")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }

    @Test
    void testCancelTaskTool() throws Exception {
        Map<String, Object> request = Map.of(
            "arguments", Map.of(
                "taskId", "test-task-id"
            )
        );

        mockMvc.perform(post("/mcp/tasks/tools/cancel_task")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }

    @Test
    void testDeleteTaskTool() throws Exception {
        Map<String, Object> request = Map.of(
            "arguments", Map.of(
                "taskId", "test-task-id"
            )
        );

        mockMvc.perform(post("/mcp/tasks/tools/delete_task")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }

    @Test
    void testRetryTaskTool() throws Exception {
        Map<String, Object> request = Map.of(
            "arguments", Map.of(
                "taskId", "test-task-id"
            )
        );

        mockMvc.perform(post("/mcp/tasks/tools/retry_task")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }
}
