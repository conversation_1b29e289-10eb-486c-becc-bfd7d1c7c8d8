package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TxtToMarkdownConverter测试类
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
class TxtToMarkdownConverterTest {
    
    @TempDir
    Path tempDir;
    
    private TxtToMarkdownConverter converter;
    private ConversionContext context;
    
    @BeforeEach
    void setUp() {
        converter = new TxtToMarkdownConverter();
        context = ConversionContext.builder().build();
    }
    
    @Test
    void testGetSupportedExtensions() {
        Set<String> extensions = converter.getSupportedExtensions();
        assertNotNull(extensions);
        assertEquals(1, extensions.size());
        assertTrue(extensions.contains("txt"));
    }
    
    @Test
    void testSupportsValidTxtFile() throws IOException {
        // 创建测试txt文件
        Path txtFile = tempDir.resolve("test.txt");
        Files.writeString(txtFile, "Test content");
        
        assertTrue(converter.supports(txtFile.toFile(), context));
    }
    
    @Test
    void testSupportsInvalidFile() {
        // 测试null文件
        assertFalse(converter.supports(null, context));
        
        // 测试不存在的文件
        File nonExistentFile = new File("nonexistent.txt");
        assertFalse(converter.supports(nonExistentFile, context));
    }
    
    @Test
    void testSupportsNonTxtFile() throws IOException {
        // 创建非txt文件
        Path docFile = tempDir.resolve("test.doc");
        Files.writeString(docFile, "Test content");
        
        assertFalse(converter.supports(docFile.toFile(), context));
    }
    
    @Test
    void testConvertSimpleTxtFile() throws IOException, ConversionException {
        // 创建测试txt文件
        String content = "Hello World!\nThis is a test file.\n\nWith multiple lines.";
        Path txtFile = tempDir.resolve("simple.txt");
        Files.writeString(txtFile, content);
        
        // 执行转换
        ConversionResult result = converter.convert(txtFile.toFile(), context);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ConversionResult.Status.SUCCESS, result.getStatus());
        assertEquals(content, result.getContent());
    }
    
    @Test
    void testConvertEmptyTxtFile() throws IOException, ConversionException {
        // 创建空txt文件
        Path txtFile = tempDir.resolve("empty.txt");
        Files.writeString(txtFile, "");
        
        // 执行转换
        ConversionResult result = converter.convert(txtFile.toFile(), context);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ConversionResult.Status.SUCCESS, result.getStatus());
        assertEquals("", result.getContent());
    }
    
    @Test
    void testConvertTxtFileWithSpecialCharacters() throws IOException, ConversionException {
        // 创建包含特殊字符的txt文件
        String content = "测试中文内容\n# 这不是标题\n* 这不是列表\n**这不是粗体**";
        Path txtFile = tempDir.resolve("special.txt");
        Files.writeString(txtFile, content);
        
        // 执行转换
        ConversionResult result = converter.convert(txtFile.toFile(), context);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ConversionResult.Status.SUCCESS, result.getStatus());
        assertEquals(content, result.getContent());
    }
    
    @Test
    void testConvertNullFile() {
        assertThrows(ConversionException.class, () -> {
            converter.convert(null, context);
        });
    }
    
    @Test
    void testConvertNonExistentFile() {
        File nonExistentFile = new File("nonexistent.txt");
        assertThrows(ConversionException.class, () -> {
            converter.convert(nonExistentFile, context);
        });
    }
    
    @Test
    void testGetCapabilities() {
        var capabilities = converter.getCapabilities();
        assertNotNull(capabilities);
        
        // 验证能力设置
        assertTrue(capabilities.getCapability("supportsBatchProcessing", false));
        assertTrue(capabilities.getCapability("supportsLargeFiles", false));
        assertFalse(capabilities.getCapability("supportsMetadataExtraction", true));
        assertFalse(capabilities.getCapability("supportsImageExtraction", true));
        assertFalse(capabilities.getCapability("supportsTableExtraction", true));
        assertEquals(100 * 1024 * 1024, capabilities.getCapability("maxFileSize", 0));
    }
    
    @Test
    void testGetMetadata() {
        var metadata = converter.getMetadata();
        assertNotNull(metadata);
        assertEquals("TXT to Markdown Converter", metadata.getName());
        assertEquals("1.0.0", metadata.getVersion());
        assertTrue(metadata.getDescription().contains("纯文本文件"));
    }
}
