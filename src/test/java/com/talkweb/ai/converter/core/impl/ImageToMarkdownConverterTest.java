package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.config.ImageConversionOptions;
import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.converter.ConversionCapabilities;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import com.talkweb.ai.converter.core.converter.ConversionMetadata;
import com.talkweb.ai.converter.core.converter.ConversionOptions;
import com.talkweb.ai.converter.model.OcrResult;
import com.talkweb.ai.converter.service.OcrService;
import com.talkweb.ai.converter.util.image.ImagePreprocessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 图像到Markdown转换器测试类
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class ImageToMarkdownConverterTest {

    @Mock
    private OcrService ocrService;

    @Mock
    private ImagePreprocessor imagePreprocessor;

    @Mock
    private com.talkweb.ai.converter.util.image.TableDetector tableDetector;

    @Mock
    private com.talkweb.ai.converter.util.image.TableExtractor tableExtractor;

    @Mock
    private com.talkweb.ai.converter.util.image.TableToMarkdownConverter tableToMarkdownConverter;

    @Mock
    private com.talkweb.ai.converter.util.image.LayoutAnalyzer layoutAnalyzer;

    @Mock
    private com.talkweb.ai.converter.util.ai.AiTextPostProcessor aiTextPostProcessor;

    @Mock
    private ConversionContext conversionContext;

    @Mock
    private ConversionOptions conversionOptions;

    private ImageToMarkdownConverter converter;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        converter = new ImageToMarkdownConverter(ocrService, imagePreprocessor,
                                                tableDetector, tableExtractor,
                                                tableToMarkdownConverter, layoutAnalyzer);

        // Setup default mocks with lenient() to avoid unnecessary stubbing exceptions
        lenient().when(conversionContext.getOptions()).thenReturn(conversionOptions);
        lenient().when(conversionOptions.getOption(eq("imageConversionOptions"), any(ImageConversionOptions.class)))
            .thenReturn(ImageConversionOptions.createDefault());
    }

    @Test
    void testGetSupportedExtensions() {
        Set<String> extensions = converter.getSupportedExtensions();

        assertNotNull(extensions);
        assertTrue(extensions.contains("png"));
        assertTrue(extensions.contains("jpg"));
        assertTrue(extensions.contains("jpeg"));
        assertTrue(extensions.contains("tiff"));
        assertTrue(extensions.contains("tif"));
        assertTrue(extensions.contains("bmp"));
        assertTrue(extensions.contains("gif"));
    }

    @Test
    void testGetCapabilities() {
        ConversionCapabilities capabilities = converter.getCapabilities();

        assertNotNull(capabilities);
        assertTrue(capabilities.supportsFeature(ConversionCapabilities.Features.IMAGES));
        assertTrue(capabilities.supportsFeature(ConversionCapabilities.Features.METADATA));
        assertTrue(capabilities.getCapability("ocrSupport", false));
        assertTrue(capabilities.getCapability("multiLanguage", false));
        assertTrue(capabilities.getCapability("imagePreprocessing", false));
        assertTrue(capabilities.getCapability("confidenceScoring", false));
        assertTrue(capabilities.getCapability("asyncProcessing", false));
        assertTrue(capabilities.getCapability("batchProcessing", false));
    }

    @Test
    void testCreateMetadata() {
        ConversionMetadata metadata = converter.createMetadata();

        assertNotNull(metadata);
        assertEquals("Image to Markdown Converter", metadata.getName());
        assertEquals("1.0", metadata.getVersion());
        assertTrue(metadata.getDescription().contains("OCR"));
    }

    @Test
    void testConvertWithNullFile() {
        assertThrows(ConversionException.class, () -> {
            converter.doConvert(null, conversionContext);
        });
    }

    @Test
    void testConvertWithNonExistentFile() {
        File nonExistentFile = new File("non_existent.png");
        lenient().when(imagePreprocessor.isSupportedFormat(nonExistentFile)).thenReturn(false);

        assertThrows(ConversionException.class, () -> {
            converter.doConvert(nonExistentFile, conversionContext);
        });
    }

    @Test
    void testConvertWithUnsupportedFormat() throws IOException {
        File textFile = tempDir.resolve("test.txt").toFile();
        textFile.createNewFile();

        when(imagePreprocessor.isSupportedFormat(textFile)).thenReturn(false);

        assertThrows(ConversionException.class, () -> {
            converter.doConvert(textFile, conversionContext);
        });
    }

    @Test
    void testConvertWithValidImageFile() throws IOException, ConversionException {
        // Create test image file
        BufferedImage testImage = createTestImage(100, 100);
        File imageFile = tempDir.resolve("test.png").toFile();
        ImageIO.write(testImage, "png", imageFile);

        // Setup mocks
        when(imagePreprocessor.isSupportedFormat(imageFile)).thenReturn(true);

        ImagePreprocessor.PreprocessingResult preprocessingResult =
            new ImagePreprocessor.PreprocessingResult(testImage, java.util.Map.of("test", "value"));
        when(imagePreprocessor.preprocessImage(any(BufferedImage.class), any()))
            .thenReturn(preprocessingResult);

        OcrResult ocrResult = OcrResult.success("Test text content", 85.0f);
        when(ocrService.recognizeText(any(BufferedImage.class))).thenReturn(ocrResult);

        // Test conversion
        ConversionResult result = converter.doConvert(imageFile, conversionContext);

        assertNotNull(result);
        assertEquals(ConversionResult.Status.SUCCESS, result.getStatus());
        assertNotNull(result.getContent());
        assertTrue(result.getContent().contains("Test text content"));
        assertTrue(result.getMessage().contains(".md") || result.getContent().length() > 0);

        // Verify interactions
        verify(imagePreprocessor).isSupportedFormat(imageFile);
        verify(imagePreprocessor).preprocessImage(any(BufferedImage.class), any());
        verify(ocrService).recognizeText(any(BufferedImage.class));
    }

    @Test
    void testConvertWithOcrDisabled() throws IOException, ConversionException {
        // Create test image file
        BufferedImage testImage = createTestImage(100, 100);
        File imageFile = tempDir.resolve("test.png").toFile();
        ImageIO.write(testImage, "png", imageFile);

        // Setup options with OCR disabled
        ImageConversionOptions options = ImageConversionOptions.createDefault();
        options.setOcrEnabled(false);
        when(conversionOptions.getOption(eq("imageConversionOptions"), any(ImageConversionOptions.class))).thenReturn(options);

        // Setup mocks
        when(imagePreprocessor.isSupportedFormat(imageFile)).thenReturn(true);

        ImagePreprocessor.PreprocessingResult preprocessingResult =
            new ImagePreprocessor.PreprocessingResult(testImage, java.util.Map.of());
        when(imagePreprocessor.preprocessImage(any(BufferedImage.class), any()))
            .thenReturn(preprocessingResult);

        // Test conversion
        ConversionResult result = converter.doConvert(imageFile, conversionContext);

        assertNotNull(result);
        assertTrue(result.getContent().contains("No text content detected"));

        // Verify OCR was not called
        verify(ocrService, never()).recognizeText(any(BufferedImage.class));
    }

    @Test
    void testConvertWithPreprocessingDisabled() throws IOException, ConversionException {
        // Create test image file
        BufferedImage testImage = createTestImage(100, 100);
        File imageFile = tempDir.resolve("test.png").toFile();
        ImageIO.write(testImage, "png", imageFile);

        // Setup options with preprocessing disabled
        ImageConversionOptions options = ImageConversionOptions.createDefault();
        options.setPreprocessingEnabled(false);
        when(conversionOptions.getOption(eq("imageConversionOptions"), any(ImageConversionOptions.class))).thenReturn(options);

        // Setup mocks
        when(imagePreprocessor.isSupportedFormat(imageFile)).thenReturn(true);

        OcrResult ocrResult = OcrResult.success("Test text", 75.0f);
        when(ocrService.recognizeText(any(BufferedImage.class))).thenReturn(ocrResult);

        // Test conversion
        ConversionResult result = converter.doConvert(imageFile, conversionContext);

        assertNotNull(result);
        assertEquals(ConversionResult.Status.SUCCESS, result.getStatus());

        // Verify preprocessing was not called
        verify(imagePreprocessor, never()).preprocessImage(any(BufferedImage.class), any());
    }

    @Test
    void testConvertWithLowConfidenceOcr() throws IOException, ConversionException {
        // Create test image file
        BufferedImage testImage = createTestImage(100, 100);
        File imageFile = tempDir.resolve("test.png").toFile();
        ImageIO.write(testImage, "png", imageFile);

        // Setup mocks
        when(imagePreprocessor.isSupportedFormat(imageFile)).thenReturn(true);

        ImagePreprocessor.PreprocessingResult preprocessingResult =
            new ImagePreprocessor.PreprocessingResult(testImage, java.util.Map.of());
        when(imagePreprocessor.preprocessImage(any(BufferedImage.class), any()))
            .thenReturn(preprocessingResult);

        // Low confidence OCR result
        OcrResult ocrResult = OcrResult.lowConfidence("Uncertain text", 30.0f);
        when(ocrService.recognizeText(any(BufferedImage.class))).thenReturn(ocrResult);

        // Test conversion
        ConversionResult result = converter.doConvert(imageFile, conversionContext);

        assertNotNull(result);
        // Accept either PARTIAL_SUCCESS or FAILED status for low confidence OCR
        assertTrue(result.getStatus() == ConversionResult.Status.PARTIAL_SUCCESS ||
                  result.getStatus() == ConversionResult.Status.FAILED);
        if (result.getStatus() == ConversionResult.Status.PARTIAL_SUCCESS) {
            assertTrue(result.getContent().contains("Uncertain text"));
        }
    }

    @Test
    void testConvertWithFailedOcr() throws IOException, ConversionException {
        // Create test image file
        BufferedImage testImage = createTestImage(100, 100);
        File imageFile = tempDir.resolve("test.png").toFile();
        ImageIO.write(testImage, "png", imageFile);

        // Setup mocks
        when(imagePreprocessor.isSupportedFormat(imageFile)).thenReturn(true);

        ImagePreprocessor.PreprocessingResult preprocessingResult =
            new ImagePreprocessor.PreprocessingResult(testImage, java.util.Map.of());
        when(imagePreprocessor.preprocessImage(any(BufferedImage.class), any()))
            .thenReturn(preprocessingResult);

        // Failed OCR result
        OcrResult ocrResult = OcrResult.failure("OCR failed");
        when(ocrService.recognizeText(any(BufferedImage.class))).thenReturn(ocrResult);

        // Test conversion
        ConversionResult result = converter.doConvert(imageFile, conversionContext);

        assertNotNull(result);
        assertEquals(ConversionResult.Status.FAILED, result.getStatus());
    }

    @Test
    void testConvertWithMetadataEnabled() throws IOException, ConversionException {
        // Create test image file
        BufferedImage testImage = createTestImage(100, 100);
        File imageFile = tempDir.resolve("test.png").toFile();
        ImageIO.write(testImage, "png", imageFile);

        // Setup options with metadata enabled
        ImageConversionOptions options = ImageConversionOptions.createDefault();
        options.setIncludeMetadata(true);
        when(conversionOptions.getOption(eq("imageConversionOptions"), any(ImageConversionOptions.class))).thenReturn(options);

        // Setup mocks
        when(imagePreprocessor.isSupportedFormat(imageFile)).thenReturn(true);

        ImagePreprocessor.PreprocessingResult preprocessingResult =
            new ImagePreprocessor.PreprocessingResult(testImage, java.util.Map.of());
        when(imagePreprocessor.preprocessImage(any(BufferedImage.class), any()))
            .thenReturn(preprocessingResult);

        OcrResult ocrResult = OcrResult.success("Test content", 80.0f);
        when(ocrService.recognizeText(any(BufferedImage.class))).thenReturn(ocrResult);

        // Test conversion
        ConversionResult result = converter.doConvert(imageFile, conversionContext);

        assertNotNull(result);
        assertTrue(result.getContent().contains("# test")); // Document header
        assertTrue(result.getContent().contains("**Source:**"));
        assertTrue(result.getContent().contains("**Processed:**"));
        assertTrue(result.getContent().contains("**OCR Confidence:**"));
    }

    @Test
    void testDefaultOptionsGetterSetter() {
        ImageConversionOptions defaultOptions = ImageConversionOptions.createHighQuality();
        converter.setDefaultOptions(defaultOptions);

        assertEquals(defaultOptions, converter.getDefaultOptions());

        // Test setting null options
        converter.setDefaultOptions(null);
        assertNotNull(converter.getDefaultOptions());
    }

    @Test
    void testSupportsExtension() {
        assertTrue(converter.supportsExtension("png"));
        assertTrue(converter.supportsExtension("jpg"));
        assertTrue(converter.supportsExtension("jpeg"));
        assertTrue(converter.supportsExtension("tiff"));
        assertTrue(converter.supportsExtension("bmp"));
        assertTrue(converter.supportsExtension("gif"));

        assertFalse(converter.supportsExtension("pdf"));
        assertFalse(converter.supportsExtension("txt"));
        assertFalse(converter.supportsExtension(null));
        assertFalse(converter.supportsExtension(""));
    }

    // Helper method to create test images
    private BufferedImage createTestImage(int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();

        // Fill with white background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);

        // Add some black text
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.PLAIN, 12));
        g2d.drawString("Test Text", 10, 20);

        g2d.dispose();
        return image;
    }
}
