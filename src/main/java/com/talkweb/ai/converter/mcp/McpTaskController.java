package com.talkweb.ai.converter.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.talkweb.ai.converter.web.model.ConversionTask;
import com.talkweb.ai.converter.web.model.TaskStatus;
import com.talkweb.ai.converter.web.service.AsyncConversionService;
import com.talkweb.ai.converter.web.service.ConversionTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP Task Management Controller
 * 
 * Provides MCP (Model Context Protocol) endpoints for task management
 * using Spring WebMVC with SSE support.
 */
@RestController
@RequestMapping("/mcp/tasks")
@Profile("server")
@ConditionalOnProperty(name = "spring.ai.mcp.server.enabled", havingValue = "true", matchIfMissing = false)
@Tag(name = "MCP Task Management", description = "MCP endpoints for conversion task management")
public class McpTaskController {

    private static final Logger logger = LoggerFactory.getLogger(McpTaskController.class);

    @Autowired
    private ConversionTaskService taskService;

    @Autowired
    private   AsyncConversionService asyncConversionService; 

    @Autowired
    private ObjectMapper objectMapper;

    // Store SSE emitters for real-time updates
    private final Map<String, SseEmitter> sseEmitters = new ConcurrentHashMap<>();

    public McpTaskController() {
        logger.info("MCP Task Controller initialized with WebMVC endpoints");
    }

    /**
     * MCP Tools Discovery - List all available tools
     */
    @PostMapping("/tools/list")
    @Operation(summary = "MCP Tools Discovery", description = "List all available MCP tools")
    public ResponseEntity<Map<String, Object>> listTools() {
        Map<String, Object> tools = new HashMap<>();

        // Define available tools
        tools.put("list_tasks", Map.of(
            "description", "List conversion tasks with pagination",
            "inputSchema", Map.of(
                "type", "object",
                "properties", Map.of(
                    "page", Map.of("type", "integer", "description", "Page number (0-based)", "default", 0),
                    "size", Map.of("type", "integer", "description", "Page size", "default", 10),
                    "status", Map.of("type", "string", "description", "Task status filter", "enum",
                        new String[]{"PENDING", "PROCESSING", "COMPLETED", "FAILED", "CANCELLED"})
                )
            )
        ));

        tools.put("get_task", Map.of(
            "description", "Get a specific conversion task by ID",
            "inputSchema", Map.of(
                "type", "object",
                "properties", Map.of(
                    "taskId", Map.of("type", "string", "description", "Task ID")
                ),
                "required", new String[]{"taskId"}
            )
        ));

        tools.put("create_task", Map.of(
            "description", "Create a new conversion task",
            "inputSchema", Map.of(
                "type", "object",
                "properties", Map.of(
                    "fileName", Map.of("type", "string", "description", "File name"),
                    "sourceFormat", Map.of("type", "string", "description", "Source format"),
                    "targetFormat", Map.of("type", "string", "description", "Target format"),
                    "priority", Map.of("type", "integer", "description", "Task priority"),
                    "options", Map.of("type", "object", "description", "Conversion options")
                ),
                "required", new String[]{"fileName", "sourceFormat", "targetFormat"}
            )
        ));

        tools.put("update_task", Map.of(
            "description", "Update an existing conversion task",
            "inputSchema", Map.of(
                "type", "object",
                "properties", Map.of(
                    "taskId", Map.of("type", "string", "description", "Task ID"),
                    "priority", Map.of("type", "integer", "description", "Task priority"),
                    "options", Map.of("type", "object", "description", "Conversion options")
                ),
                "required", new String[]{"taskId"}
            )
        ));

        tools.put("delete_task", Map.of(
            "description", "Delete a conversion task",
            "inputSchema", Map.of(
                "type", "object",
                "properties", Map.of(
                    "taskId", Map.of("type", "string", "description", "Task ID")
                ),
                "required", new String[]{"taskId"}
            )
        ));

        tools.put("get_task_status", Map.of(
            "description", "Get the status of a conversion task",
            "inputSchema", Map.of(
                "type", "object",
                "properties", Map.of(
                    "taskId", Map.of("type", "string", "description", "Task ID")
                ),
                "required", new String[]{"taskId"}
            )
        ));

        tools.put("cancel_task", Map.of(
            "description", "Cancel a running conversion task",
            "inputSchema", Map.of(
                "type", "object",
                "properties", Map.of(
                    "taskId", Map.of("type", "string", "description", "Task ID")
                ),
                "required", new String[]{"taskId"}
            )
        ));

        Map<String, Object> response = new HashMap<>();
        response.put("tools", tools);

        logger.debug("MCP: Listed {} available tools", tools.size());
        return ResponseEntity.ok(response);
    }

    /**
     * MCP Tool: List conversion tasks with pagination
     */
    @PostMapping("/tools/list_tasks")
    @Operation(summary = "MCP Tool: List Tasks", description = "List conversion tasks with pagination")
    public ResponseEntity<Map<String, Object>> listTasks(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) request.getOrDefault("arguments", new HashMap<>());

            int page = (Integer) params.getOrDefault("page", 0);
            int size = (Integer) params.getOrDefault("size", 10);
            String status = (String) params.get("status");
            String sortBy = (String) params.getOrDefault("sortBy", "createdAt");
            String sortDir = (String) params.getOrDefault("sortDir", "desc");

            if (status != null && !status.isEmpty()) {
                TaskStatus taskStatus = TaskStatus.valueOf(status.toUpperCase());
                var taskPage = taskService.getTasksByStatus(taskStatus, page, size);

                Map<String, Object> result = new HashMap<>();
                result.put("content", Map.of(
                    "tasks", taskPage.getContent(),
                    "totalElements", taskPage.getTotalElements(),
                    "totalPages", taskPage.getTotalPages(),
                    "currentPage", taskPage.getNumber(),
                    "size", taskPage.getSize()
                ));

                return ResponseEntity.ok(result);
            } else {
                var taskPage = taskService.getAllTasks(page, size, sortBy, sortDir);

                Map<String, Object> result = new HashMap<>();
                result.put("content", Map.of(
                    "tasks", taskPage.getContent(),
                    "totalElements", taskPage.getTotalElements(),
                    "totalPages", taskPage.getTotalPages(),
                    "currentPage", taskPage.getNumber(),
                    "size", taskPage.getSize()
                ));

                logger.debug("MCP: Listed {} tasks (page {}/{})", taskPage.getNumberOfElements(),
                            page + 1, taskPage.getTotalPages());

                return ResponseEntity.ok(result);
            }
        } catch (Exception e) {
            logger.error("MCP: Error listing tasks", e);
            return ResponseEntity.ok(Map.of("content", Map.of("error", "Failed to list tasks: " + e.getMessage())));
        }
    }

    /**
     * MCP Tool: Get a specific conversion task by ID
     */
    @PostMapping("/tools/get_task")
    @Operation(summary = "MCP Tool: Get Task", description = "Get a specific conversion task by ID")
    public ResponseEntity<Map<String, Object>> getTask(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) request.getOrDefault("arguments", new HashMap<>());

            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return ResponseEntity.ok(Map.of("content", Map.of("error", "Task ID is required")));
            }

            var taskResponse = taskService.getTaskResponse(taskId);
            logger.debug("MCP: Retrieved task: {}", taskId);
            return ResponseEntity.ok(Map.of("content", Map.of("task", taskResponse)));
        } catch (Exception e) {
            logger.error("MCP: Error getting task", e);
            return ResponseEntity.ok(Map.of("content", Map.of("error", "Failed to get task: " + e.getMessage())));
        }
    }

    /**
     * MCP Tool: Create a new conversion task
     */
    @PostMapping("/tools/create_task")
    @Operation(summary = "MCP Tool: Create Task", description = "Create a new conversion task")
    public ResponseEntity<Map<String, Object>> createTask(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) request.getOrDefault("arguments", new HashMap<>());

            String fileName = (String) params.get("fileName");
            String filePath = (String) params.getOrDefault("filePath", "/tmp/" + fileName);
            Long fileSize = params.containsKey("fileSize") ?
                Long.valueOf(params.get("fileSize").toString()) : 0L;
            String fileType = (String) params.getOrDefault("fileType", "unknown");

            if (fileName == null) {
                return ResponseEntity.ok(Map.of("content", Map.of("error", "fileName is required")));
            }

            // Create task using the service
            String targetFileName = (String) params.get("targetFileName");
            String conversionOptions = null;

            if (params.containsKey("options")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> options = (Map<String, Object>) params.get("options");
                conversionOptions = objectMapper.writeValueAsString(options);
            }

            ConversionTask savedTask = taskService.createTask(fileName, filePath, fileSize, fileType,
                                                            targetFileName, conversionOptions);

            logger.info("MCP: Created new task: {} for file: {}", savedTask.getTaskId(), fileName);

              // Start async conversion
            CompletableFuture<ConversionTask> conversionFuture =
                asyncConversionService.convertDocumentAsync(savedTask.getTaskId());


            // Notify SSE clients
            notifySseClients("task_created", Map.of("task", savedTask));

            return ResponseEntity.ok(Map.of("content", Map.of(
                "task", savedTask,
                "message", "Task created successfully"
            )));
        } catch (Exception e) {
            logger.error("MCP: Error creating task", e);
            return ResponseEntity.ok(Map.of("content", Map.of("error", "Failed to create task: " + e.getMessage())));
        }
    }

    /**
     * MCP Tool: Update an existing conversion task (Limited functionality)
     */
    @PostMapping("/tools/update_task")
    @Operation(summary = "MCP Tool: Update Task", description = "Update an existing conversion task")
    public ResponseEntity<Map<String, Object>> updateTask(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) request.getOrDefault("arguments", new HashMap<>());

            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return ResponseEntity.ok(Map.of("content", Map.of("error", "Task ID is required")));
            }

            // For now, we can only update progress through the existing service
            if (params.containsKey("progress")) {
                int progress = (Integer) params.get("progress");
                ConversionTask updatedTask = taskService.updateTaskProgress(taskId, progress);

              
                logger.info("MCP: Updated task progress: {}", taskId);

                // Notify SSE clients
                notifySseClients("task_updated", Map.of("task", updatedTask));

                return ResponseEntity.ok(Map.of("content", Map.of(
                    "task", updatedTask,
                    "message", "Task progress updated successfully"
                )));
            } else {
                return ResponseEntity.ok(Map.of("content", Map.of(
                    "message", "No supported update fields provided. Only 'progress' is currently supported."
                )));
            }
        } catch (Exception e) {
            logger.error("MCP: Error updating task", e);
            return ResponseEntity.ok(Map.of("content", Map.of("error", "Failed to update task: " + e.getMessage())));
        }
    }

    /**
     * MCP Tool: Delete a conversion task
     */
    @PostMapping("/tools/delete_task")
    @Operation(summary = "MCP Tool: Delete Task", description = "Delete a conversion task")
    public ResponseEntity<Map<String, Object>> deleteTask(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) request.getOrDefault("arguments", new HashMap<>());

            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return ResponseEntity.ok(Map.of("content", Map.of("error", "Task ID is required")));
            }
            asyncConversionService.deleteConversion(taskId) ;

          //  taskService.deleteTask(taskId);
            logger.info("MCP: Deleted task: {}", taskId);

            
            // Notify SSE clients
            notifySseClients("task_deleted", Map.of("taskId", taskId));

            return ResponseEntity.ok(Map.of("content", Map.of(
                "message", "Task deleted successfully",
                "taskId", taskId
            )));
        } catch (Exception e) {
            logger.error("MCP: Error deleting task", e);
            return ResponseEntity.ok(Map.of("content", Map.of("error", "Failed to delete task: " + e.getMessage())));
        }
    }

    /**
     * MCP Tool: Get the status of a conversion task
     */
    @PostMapping("/tools/get_task_status")
    @Operation(summary = "MCP Tool: Get Task Status", description = "Get the status of a conversion task")
    public ResponseEntity<Map<String, Object>> getTaskStatus(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) request.getOrDefault("arguments", new HashMap<>());

            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return ResponseEntity.ok(Map.of("content", Map.of("error", "Task ID is required")));
            }

            var progressResponse = taskService.getTaskProgress(taskId);
            Map<String, Object> status = new HashMap<>();
            status.put("taskId", progressResponse.getTaskId());
            status.put("status", progressResponse.getStatus());
            status.put("progress", progressResponse.getProgress());
            status.put("lastUpdated", progressResponse.getLastUpdated());
            if (progressResponse.getErrorMessage() != null) {
                status.put("errorMessage", progressResponse.getErrorMessage());
            }

            return ResponseEntity.ok(Map.of("content", status));
        } catch (Exception e) {
            logger.error("MCP: Error getting task status", e);
            return ResponseEntity.ok(Map.of("content", Map.of("error", "Failed to get task status: " + e.getMessage())));
        }
    }

    /**
     * MCP Tool: Cancel a running conversion task
     */
    @PostMapping("/tools/cancel_task")
    @Operation(summary = "MCP Tool: Cancel Task", description = "Cancel a running conversion task")
    public ResponseEntity<Map<String, Object>> cancelTask(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) request.getOrDefault("arguments", new HashMap<>());

            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return ResponseEntity.ok(Map.of("content", Map.of("error", "Task ID is required")));
            }

            ConversionTask cancelledTask = taskService.cancelTask(taskId);
            logger.info("MCP: Cancelled task: {}", taskId);

            // Notify SSE clients
            notifySseClients("task_cancelled", Map.of("task", cancelledTask));

            return ResponseEntity.ok(Map.of("content", Map.of(
                "message", "Task cancelled successfully",
                "task", cancelledTask
            )));
        } catch (Exception e) {
            logger.error("MCP: Error cancelling task", e);
            return ResponseEntity.ok(Map.of("content", Map.of("error", "Failed to cancel task: " + e.getMessage())));
        }
    }

    /**
     * MCP Tool: Retry a failed conversion task
     */
    @PostMapping("/tools/retry_task")
    @Operation(summary = "MCP Tool: Retry Task", description = "Retry a failed conversion task")
    public ResponseEntity<Map<String, Object>> retryTask(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) request.getOrDefault("arguments", new HashMap<>());

            String taskId = (String) params.get("taskId");
            if (taskId == null || taskId.isEmpty()) {
                return ResponseEntity.ok(Map.of("content", Map.of("error", "Task ID is required")));
            }

            // Update task status to PENDING to retry
            ConversionTask retriedTask = taskService.updateTaskStatus(taskId, TaskStatus.PENDING);
            logger.info("MCP: Retrying task: {}", taskId);

            // Notify SSE clients
            notifySseClients("task_retried", Map.of("task", retriedTask));

            return ResponseEntity.ok(Map.of("content", Map.of(
                "task", retriedTask,
                "message", "Task queued for retry"
            )));
        } catch (Exception e) {
            logger.error("MCP: Error retrying task", e);
            return ResponseEntity.ok(Map.of("content", Map.of("error", "Failed to retry task: " + e.getMessage())));
        }
    }

    /**
     * SSE endpoint for real-time task updates
     */
    @GetMapping(value = "/events", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "MCP SSE Events", description = "Stream real-time task events")
    public SseEmitter streamEvents(@Parameter(description = "Client ID") @RequestParam(required = false) String clientId) {
        String id = clientId != null ? clientId : "client_" + System.currentTimeMillis();
        
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        sseEmitters.put(id, emitter);
        
        logger.info("MCP: SSE client connected: {}", id);
        
        emitter.onCompletion(() -> {
            sseEmitters.remove(id);
            logger.info("MCP: SSE client disconnected: {}", id);
        });
        
        emitter.onTimeout(() -> {
            sseEmitters.remove(id);
            logger.info("MCP: SSE client timeout: {}", id);
        });
        
        emitter.onError((ex) -> {
            sseEmitters.remove(id);
            logger.error("MCP: SSE client error: {}", id, ex);
        });
        
        // Send initial connection event
        try {
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data(Map.of("message", "Connected to MCP task events", "clientId", id)));
        } catch (Exception e) {
            logger.error("MCP: Error sending initial SSE event", e);
        }
        
        return emitter;
    }

    /**
     * Notify all SSE clients about task events
     */
    private void notifySseClients(String eventType, Map<String, Object> data) {
        if (sseEmitters.isEmpty()) {
            return;
        }
        
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("type", eventType);
        eventData.put("timestamp", LocalDateTime.now());
        eventData.putAll(data);
        
        sseEmitters.entrySet().removeIf(entry -> {
            try {
                entry.getValue().send(SseEmitter.event()
                        .name(eventType)
                        .data(eventData));
                return false;
            } catch (Exception e) {
                logger.debug("MCP: Failed to send SSE event to client {}: {}", entry.getKey(), e.getMessage());
                return true; // Remove failed emitter
            }
        });
        
        logger.debug("MCP: Notified {} SSE clients about {}", sseEmitters.size(), eventType);
    }
}
