package com.talkweb.ai.converter.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * MCP Client for Task Management
 * 
 * Example client demonstrating how to interact with the Task Management MCP Server
 * through SSE (Server-Sent Events) transport.
 */
@Component
@ConditionalOnProperty(name = "spring.ai.mcp.server.enabled", havingValue = "true", matchIfMissing = false)
public class McpTaskClient {

    private static final Logger logger = LoggerFactory.getLogger(McpTaskClient.class);
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public McpTaskClient(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.webClient = WebClient.builder()
                .baseUrl("http://localhost:8081")
                .build();
        
        logger.info("MCP Task Client initialized for endpoint: http://localhost:8081/mcp/tasks");
    }

    /**
     * Connect to MCP SSE endpoint and listen for events
     */
    public Flux<String> connectToMcpServer() {
        return webClient.get()
                .uri("/mcp/tasks")
                .accept(org.springframework.http.MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class)
                .doOnSubscribe(subscription -> logger.info("Connected to MCP SSE endpoint"))
                .doOnNext(event -> logger.debug("Received SSE event: {}", event))
                .doOnError(error -> logger.error("SSE connection error", error))
                .doOnComplete(() -> logger.info("SSE connection completed"))
                .timeout(Duration.ofMinutes(5))
                .retry(3);
    }

    /**
     * Send a tool call request to the MCP server
     */
    public Mono<Map<String, Object>> callTool(String toolName, Map<String, Object> parameters) {
        Map<String, Object> request = new HashMap<>();
        request.put("method", "tools/call");
        request.put("params", Map.of(
            "name", toolName,
            "arguments", parameters
        ));

        return webClient.post()
                .uri("/mcp/tasks")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    try {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> result = (Map<String, Object>) objectMapper.readValue(response, Map.class);
                        return result;
                    } catch (Exception e) {
                        logger.error("Error parsing MCP response", e);
                        Map<String, Object> errorResult = new HashMap<>();
                        errorResult.put("error", "Failed to parse response: " + e.getMessage());
                        return errorResult;
                    }
                })
                .doOnSuccess(result -> logger.debug("Tool call {} completed: {}", toolName, result))
                .doOnError(error -> logger.error("Tool call {} failed", toolName, error));
    }

    /**
     * List all conversion tasks
     */
    public Mono<Map<String, Object>> listTasks(int page, int size, String status) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("size", size);
        if (status != null && !status.isEmpty()) {
            params.put("status", status);
        }

        return callTool("list_tasks", params);
    }

    /**
     * Get a specific task by ID
     */
    public Mono<Map<String, Object>> getTask(String taskId) {
        return callTool("get_task", Map.of("taskId", taskId));
    }

    /**
     * Create a new conversion task
     */
    public Mono<Map<String, Object>> createTask(String fileName, String sourceFormat, 
                                               String targetFormat, Integer priority, 
                                               Map<String, Object> options) {
        Map<String, Object> params = new HashMap<>();
        params.put("fileName", fileName);
        params.put("sourceFormat", sourceFormat);
        params.put("targetFormat", targetFormat);
        
        if (priority != null) {
            params.put("priority", priority);
        }
        if (options != null && !options.isEmpty()) {
            params.put("options", options);
        }

        return callTool("create_task", params);
    }

    /**
     * Update an existing task
     */
    public Mono<Map<String, Object>> updateTask(String taskId, Integer priority, 
                                               Map<String, Object> options) {
        Map<String, Object> params = new HashMap<>();
        params.put("taskId", taskId);
        
        if (priority != null) {
            params.put("priority", priority);
        }
        if (options != null && !options.isEmpty()) {
            params.put("options", options);
        }

        return callTool("update_task", params);
    }

    /**
     * Delete a task
     */
    public Mono<Map<String, Object>> deleteTask(String taskId) {
        return callTool("delete_task", Map.of("taskId", taskId));
    }

    /**
     * Get task status
     */
    public Mono<Map<String, Object>> getTaskStatus(String taskId) {
        return callTool("get_task_status", Map.of("taskId", taskId));
    }

    /**
     * Cancel a running task
     */
    public Mono<Map<String, Object>> cancelTask(String taskId) {
        return callTool("cancel_task", Map.of("taskId", taskId));
    }

    /**
     * Retry a failed task
     */
    public Mono<Map<String, Object>> retryTask(String taskId) {
        return callTool("retry_task", Map.of("taskId", taskId));
    }

    /**
     * Example usage method demonstrating the MCP client functionality
     */
    public void demonstrateUsage() {
        logger.info("=== MCP Task Management Client Demo ===");

        // Example 1: List all tasks
        listTasks(0, 10, null)
                .subscribe(
                    result -> logger.info("Listed tasks: {}", result),
                    error -> logger.error("Failed to list tasks", error)
                );

        // Example 2: Create a new task
        Map<String, Object> options = Map.of(
            "quality", "high",
            "preserveFormatting", true
        );
        
        createTask("example.pdf", "PDF", "MARKDOWN", 1, options)
                .subscribe(
                    result -> {
                        logger.info("Created task: {}", result);
                        
                        // Example 3: Get the created task's status
                        @SuppressWarnings("unchecked")
                        Map<String, Object> task = (Map<String, Object>) result.get("task");
                        if (task != null) {
                            String taskId = (String) task.get("id");
                            getTaskStatus(taskId)
                                    .subscribe(
                                        status -> logger.info("Task status: {}", status),
                                        error -> logger.error("Failed to get task status", error)
                                    );
                        }
                    },
                    error -> logger.error("Failed to create task", error)
                );

        // Example 4: Connect to SSE stream for real-time updates
        connectToMcpServer()
                .take(10) // Take only first 10 events for demo
                .subscribe(
                    event -> logger.info("Received real-time event: {}", event),
                    error -> logger.error("SSE stream error", error),
                    () -> logger.info("SSE stream completed")
                );
    }
}
