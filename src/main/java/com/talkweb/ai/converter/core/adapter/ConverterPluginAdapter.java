package com.talkweb.ai.converter.core.adapter;

import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.DocumentProcessor;
import com.talkweb.ai.converter.core.DocumentProcessingException;
import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.PluginContext;
import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginMetadata;
import com.talkweb.ai.converter.core.PluginState;
import com.talkweb.ai.converter.core.ProcessingContext;
import com.talkweb.ai.converter.core.ProcessingResult;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import com.talkweb.ai.converter.core.converter.ConversionOptions;
import com.talkweb.ai.converter.core.converter.DocumentConverter;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 转换器插件适配器，将DocumentConverter适配为Plugin和DocumentProcessor接口
 *
 * 这个适配器将转换器逻辑与插件管理逻辑分离，实现了关注点分离原则。
 * 转换器只需要关注文档转换逻辑，而插件生命周期管理由适配器处理。
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ConverterPluginAdapter implements DocumentProcessor {
    
    private static final Logger logger = Logger.getLogger(ConverterPluginAdapter.class.getName());
    
    private final DocumentConverter converter;
    private final PluginMetadata metadata;
    private PluginState state = PluginState.LOADED;
    private PluginContext context;
    private Map<String, Object> configuration = new HashMap<>();
    
    /**
     * 创建转换器插件适配器
     * 
     * @param converter 文档转换器实例
     * @param metadata 插件元数据
     */
    public ConverterPluginAdapter(DocumentConverter converter, PluginMetadata metadata) {
        this.converter = converter;
        this.metadata = metadata;
        
        if (converter == null) {
            throw new IllegalArgumentException("Converter cannot be null");
        }
        if (metadata == null) {
            throw new IllegalArgumentException("Metadata cannot be null");
        }
        
        logger.info("Created converter plugin adapter for: " + metadata.getName());
    }
    
    /**
     * 获取被适配的转换器实例
     * 
     * @return 文档转换器实例
     */
    public DocumentConverter getConverter() {
        return converter;
    }
    
    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public PluginState getState() {
        return state;
    }
    
    @Override
    public void init(PluginContext context) throws PluginException {
        try {
            logger.info("Initializing converter plugin: " + metadata.getName());
            
            this.context = context;
            
            // 如果转换器实现了可初始化接口，则调用初始化方法
            if (converter instanceof Initializable) {
                ((Initializable) converter).initialize(context);
            }
            
            this.state = PluginState.READY;
            
            logger.info("Successfully initialized converter plugin: " + metadata.getName());
            
        } catch (Exception e) {
            this.state = PluginState.ERROR;
            String errorMsg = "Failed to initialize converter plugin: " + metadata.getName();
            logger.severe(errorMsg + " - " + e.getMessage());
            throw new PluginException(errorMsg, e);
        }
    }
    
    @Override
    public void start() throws PluginException {
        try {
            logger.info("Starting converter plugin: " + metadata.getName());
            
            if (state != PluginState.READY) {
                throw new PluginException("Plugin must be in READY state to start, current state: " + state);
            }
            
            // 如果转换器实现了可启动接口，则调用启动方法
            if (converter instanceof Startable) {
                ((Startable) converter).start();
            }
            
            this.state = PluginState.RUNNING;
            
            logger.info("Successfully started converter plugin: " + metadata.getName());
            
        } catch (Exception e) {
            this.state = PluginState.ERROR;
            String errorMsg = "Failed to start converter plugin: " + metadata.getName();
            logger.severe(errorMsg + " - " + e.getMessage());
            throw new PluginException(errorMsg, e);
        }
    }
    
    @Override
    public void stop() throws PluginException {
        try {
            logger.info("Stopping converter plugin: " + metadata.getName());
            
            // 如果转换器实现了可停止接口，则调用停止方法
            if (converter instanceof Stoppable) {
                ((Stoppable) converter).stop();
            }
            
            this.state = PluginState.STOPPED;
            
            logger.info("Successfully stopped converter plugin: " + metadata.getName());
            
        } catch (Exception e) {
            this.state = PluginState.ERROR;
            String errorMsg = "Failed to stop converter plugin: " + metadata.getName();
            logger.severe(errorMsg + " - " + e.getMessage());
            throw new PluginException(errorMsg, e);
        }
    }
    
    @Override
    public void destroy() throws PluginException {
        try {
            logger.info("Destroying converter plugin: " + metadata.getName());
            
            // 调用转换器的销毁方法
            converter.destroy();
            
            this.state = PluginState.DESTROYED;
            this.context = null;
            
            logger.info("Successfully destroyed converter plugin: " + metadata.getName());
            
        } catch (Exception e) {
            this.state = PluginState.ERROR;
            String errorMsg = "Failed to destroy converter plugin: " + metadata.getName();
            logger.severe(errorMsg + " - " + e.getMessage());
            throw new PluginException(errorMsg, e);
        }
    }
    
    /**
     * 可初始化接口，转换器可以选择实现此接口来接收初始化通知
     */
    public interface Initializable {
        void initialize(PluginContext context) throws Exception;
    }
    
    /**
     * 可启动接口，转换器可以选择实现此接口来接收启动通知
     */
    public interface Startable {
        void start() throws Exception;
    }
    
    /**
     * 可停止接口，转换器可以选择实现此接口来接收停止通知
     */
    public interface Stoppable {
        void stop() throws Exception;
    }
    
    // DocumentProcessor interface implementation

    @Override
    public String[] getSupportedExtensions() {
        return converter.getSupportedExtensions().toArray(new String[0]);
    }

    @Override
    public boolean supports(String extension) {
        return converter.supportsExtension(extension);
    }

    @Override
    public ProcessingResult process(File inputFile, ProcessingContext context) throws DocumentProcessingException {
        try {
            // 将ProcessingContext转换为ConversionContext
            ConversionContext conversionContext = adaptProcessingContext(context);

            // 执行转换
            ConversionResult conversionResult = converter.convert(inputFile, conversionContext);

            // 将ConversionResult转换为ProcessingResult
            return adaptConversionResult(conversionResult, inputFile, context);

        } catch (ConversionException e) {
            throw new DocumentProcessingException("Failed to process document: " + inputFile.getName(), e);
        } catch (Exception e) {
            throw new DocumentProcessingException("Unexpected error processing document: " + inputFile.getName(), e);
        }
    }

    @Override
    public Map<String, Object> getConfiguration() {
        return new HashMap<>(configuration);
    }

    @Override
    public void configure(Map<String, Object> config) {
        if (config != null) {
            this.configuration.clear();
            this.configuration.putAll(config);
        }
    }

    /**
     * 将ProcessingContext适配为ConversionContext
     */
    private ConversionContext adaptProcessingContext(ProcessingContext processingContext) {
        ConversionContext.Builder builder = ConversionContext.builder();

        // 创建转换选项
        ConversionOptions.Builder optionsBuilder = ConversionOptions.builder();

        // 复制相关属性
        if (processingContext.isExtractMetadata()) {
            optionsBuilder.option("extractMetadata", true);
        }
        if (processingContext.isExtractImages()) {
            optionsBuilder.option("extractImages", true);
        }
        if (processingContext.isExtractTables()) {
            optionsBuilder.option("extractTables", true);
        }
        if (processingContext.isPreserveImages()) {
            optionsBuilder.option("preserveImages", true);
        }

        builder.options(optionsBuilder.build());

        return builder.build();
    }

    /**
     * 将ConversionResult适配为ProcessingResult
     */
    private ProcessingResult adaptConversionResult(ConversionResult conversionResult, File inputFile, ProcessingContext context) {
        if (conversionResult.isSuccess()) {
            // 创建输出文件
            File outputFile = createOutputFile(inputFile, context);

            // 写入转换结果到输出文件
            try {
                java.nio.file.Files.writeString(outputFile.toPath(), conversionResult.getContent());
                return ProcessingResult.success(outputFile);
            } catch (Exception e) {
                return ProcessingResult.failure("Failed to write output file: " + e.getMessage());
            }
        } else {
            return ProcessingResult.failure(conversionResult.getMessage());
        }
    }

    /**
     * 创建输出文件
     */
    private File createOutputFile(File inputFile, ProcessingContext context) {
        String fileName = inputFile.getName();
        String baseName = fileName.contains(".") ?
            fileName.substring(0, fileName.lastIndexOf('.')) : fileName;

        if (context.getOutputPath() != null) {
            return context.getOutputPath().resolve(baseName + ".md").toFile();
        } else {
            // 默认在输入文件同目录下创建
            String parentDir = inputFile.getParent();
            if (parentDir == null) {
                parentDir = System.getProperty("user.dir");
            }
            return new File(parentDir, baseName + ".md");
        }
    }

    @Override
    public String toString() {
        return "ConverterPluginAdapter{" +
                "converter=" + converter.getClass().getSimpleName() +
                ", metadata=" + metadata +
                ", state=" + state +
                '}';
    }
}
