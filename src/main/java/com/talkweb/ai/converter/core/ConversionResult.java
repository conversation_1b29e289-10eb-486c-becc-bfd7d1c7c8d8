

package com.talkweb.ai.converter.core;

/**
 * 文档转换结果封装类
 */
public class ConversionResult {
    public enum Status {
        SUCCESS,
        FAILED,
        PARTIAL_SUCCESS
    }

    private final Status status;
    private final String content;
    private final boolean success;
    private final String message;

    // AI-generated analysis fields
    private String summary;
    private String keyPoints;
    private String analysis;

    private String enhancedContent;

    public ConversionResult(Status status, String content, String message) {
        this.status = status;
        this.content = content;
        this.success = status == Status.SUCCESS || status == Status.PARTIAL_SUCCESS;
        this.message = message;
    }

    public ConversionResult(String content, boolean success, String message) {
        this(success ? Status.SUCCESS : Status.FAILED, content, message);
    }

    public ConversionResult(Status status, String inputPath, String outputPath, String content) {
        this(status, content, outputPath);
    }

    public Status getStatus() {
        return status;
    }

    // Getters
    public String getEnhancedContent() { return enhancedContent != null ? enhancedContent : content; }
    public void setEnhancedContent(String enhancedContent) { this.enhancedContent = enhancedContent; }
    public String getContent() { return content; }
    public String getMarkdownContent() { return content; }
    public boolean isSuccess() { return success; }
    public String getMessage() { return message; }

    // AI analysis getters and setters
    public String getSummary() { return summary; }
    public void setSummary(String summary) { this.summary = summary; }

    public String getKeyPoints() { return keyPoints; }
    public void setKeyPoints(String keyPoints) { this.keyPoints = keyPoints; }

    public String getAnalysis() { return analysis; }
    public void setAnalysis(String analysis) { this.analysis = analysis; }

    /**
     * Checks if AI analysis is available
     */
    public boolean hasAiAnalysis() {
        return summary != null || keyPoints != null || analysis != null;
    }
}

