package com.talkweb.ai.converter.web.controller;

import com.talkweb.ai.converter.mcp.McpTaskClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * MCP Demo Controller
 * 
 * Demonstrates the usage of MCP (Model Context Protocol) for task management
 * through SSE (Server-Sent Events) transport.
 */
@RestController
@RequestMapping("/api/mcp")
@Profile("server")
@ConditionalOnProperty(name = "spring.ai.mcp.server.enabled", havingValue = "true", matchIfMissing = false)
@Tag(name = "MCP Demo", description = "MCP (Model Context Protocol) task management demonstration")
public class McpDemoController {

    private static final Logger logger = LoggerFactory.getLogger(McpDemoController.class);

    @Autowired
    private McpTaskClient mcpTaskClient;

    @GetMapping("/demo")
    @Operation(summary = "Run MCP client demonstration", 
               description = "Demonstrates various MCP task management operations")
    public ResponseEntity<String> runDemo() {
        try {
            mcpTaskClient.demonstrateUsage();
            return ResponseEntity.ok("MCP demonstration started. Check logs for details.");
        } catch (Exception e) {
            logger.error("Error running MCP demo", e);
            return ResponseEntity.internalServerError()
                    .body("Failed to run MCP demo: " + e.getMessage());
        }
    }

    @GetMapping("/tasks")
    @Operation(summary = "List tasks via MCP", 
               description = "List conversion tasks using MCP client")
    public Mono<ResponseEntity<Map<String, Object>>> listTasks(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Task status filter") @RequestParam(required = false) String status) {
        
        return mcpTaskClient.listTasks(page, size, status)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    @GetMapping("/tasks/{taskId}")
    @Operation(summary = "Get task via MCP", 
               description = "Get a specific task using MCP client")
    public Mono<ResponseEntity<Map<String, Object>>> getTask(
            @Parameter(description = "Task ID") @PathVariable String taskId) {
        
        return mcpTaskClient.getTask(taskId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    @PostMapping("/tasks")
    @Operation(summary = "Create task via MCP", 
               description = "Create a new conversion task using MCP client")
    public Mono<ResponseEntity<Map<String, Object>>> createTask(
            @RequestBody CreateTaskRequest request) {
        
        return mcpTaskClient.createTask(
                request.getFileName(),
                request.getSourceFormat(),
                request.getTargetFormat(),
                request.getPriority(),
                request.getOptions()
        )
        .map(ResponseEntity::ok)
        .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    @PutMapping("/tasks/{taskId}")
    @Operation(summary = "Update task via MCP", 
               description = "Update an existing task using MCP client")
    public Mono<ResponseEntity<Map<String, Object>>> updateTask(
            @Parameter(description = "Task ID") @PathVariable String taskId,
            @RequestBody UpdateTaskRequest request) {
        
        return mcpTaskClient.updateTask(taskId, request.getPriority(), request.getOptions())
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    @DeleteMapping("/tasks/{taskId}")
    @Operation(summary = "Delete task via MCP", 
               description = "Delete a task using MCP client")
    public Mono<ResponseEntity<Map<String, Object>>> deleteTask(
            @Parameter(description = "Task ID") @PathVariable String taskId) {
        
        return mcpTaskClient.deleteTask(taskId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    @GetMapping("/tasks/{taskId}/status")
    @Operation(summary = "Get task status via MCP", 
               description = "Get task status using MCP client")
    public Mono<ResponseEntity<Map<String, Object>>> getTaskStatus(
            @Parameter(description = "Task ID") @PathVariable String taskId) {
        
        return mcpTaskClient.getTaskStatus(taskId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    @PostMapping("/tasks/{taskId}/cancel")
    @Operation(summary = "Cancel task via MCP", 
               description = "Cancel a running task using MCP client")
    public Mono<ResponseEntity<Map<String, Object>>> cancelTask(
            @Parameter(description = "Task ID") @PathVariable String taskId) {
        
        return mcpTaskClient.cancelTask(taskId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    @PostMapping("/tasks/{taskId}/retry")
    @Operation(summary = "Retry task via MCP", 
               description = "Retry a failed task using MCP client")
    public Mono<ResponseEntity<Map<String, Object>>> retryTask(
            @Parameter(description = "Task ID") @PathVariable String taskId) {
        
        return mcpTaskClient.retryTask(taskId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    @GetMapping(value = "/events", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "MCP SSE Events", 
               description = "Stream real-time events from MCP server")
    public Flux<String> streamMcpEvents() {
        return mcpTaskClient.connectToMcpServer()
                .doOnSubscribe(subscription -> logger.info("Client connected to MCP SSE stream"))
                .doOnCancel(() -> logger.info("Client disconnected from MCP SSE stream"));
    }

    // Request DTOs
    public static class CreateTaskRequest {
        private String fileName;
        private String sourceFormat;
        private String targetFormat;
        private Integer priority;
        private Map<String, Object> options;

        // Getters and setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public String getSourceFormat() { return sourceFormat; }
        public void setSourceFormat(String sourceFormat) { this.sourceFormat = sourceFormat; }
        
        public String getTargetFormat() { return targetFormat; }
        public void setTargetFormat(String targetFormat) { this.targetFormat = targetFormat; }
        
        public Integer getPriority() { return priority; }
        public void setPriority(Integer priority) { this.priority = priority; }
        
        public Map<String, Object> getOptions() { return options; }
        public void setOptions(Map<String, Object> options) { this.options = options; }
    }

    public static class UpdateTaskRequest {
        private Integer priority;
        private Map<String, Object> options;

        // Getters and setters
        public Integer getPriority() { return priority; }
        public void setPriority(Integer priority) { this.priority = priority; }
        
        public Map<String, Object> getOptions() { return options; }
        public void setOptions(Map<String, Object> options) { this.options = options; }
    }
}
