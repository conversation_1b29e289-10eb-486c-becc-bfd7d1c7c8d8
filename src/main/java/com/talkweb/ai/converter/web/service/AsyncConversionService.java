package com.talkweb.ai.converter.web.service;

import com.talkweb.ai.converter.core.DocumentProcessor;
import com.talkweb.ai.converter.core.PluginManager;
import com.talkweb.ai.converter.core.ProcessingContext;
import com.talkweb.ai.converter.core.ProcessingResult;
import com.talkweb.ai.converter.core.impl.DocumentProcessingChain;
import com.talkweb.ai.converter.web.exception.ConversionException;
import com.talkweb.ai.converter.web.model.ConversionTask;
import com.talkweb.ai.converter.web.model.TaskStatus;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * Service for handling asynchronous document conversion
 */
@Service
@Profile("server")
public class AsyncConversionService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncConversionService.class);

    private final ConversionTaskService taskService;
    private final FileStorageService fileStorageService;
    private final TaskProgressService progressService;
    private final PluginManager pluginManager;

    // Track running tasks for cancellation
    private final Map<String, Future<?>> runningTasks = new ConcurrentHashMap<>();

    public AsyncConversionService(
            ConversionTaskService taskService,
            FileStorageService fileStorageService,
            TaskProgressService progressService,
            PluginManager pluginManager) {
        this.taskService = taskService;
        this.fileStorageService = fileStorageService;
        this.progressService = progressService;
        this.pluginManager = pluginManager;
    }

    /**
     * Start asynchronous conversion of a document
     */
    @Async("conversionTaskExecutor")
    public CompletableFuture<ConversionTask> convertDocumentAsync(String taskId) {
        logger.info("Starting async conversion for task {}", taskId);
        
        try {
            ConversionTask task = taskService.getTask(taskId);
            
            // Register the task as running
            Future<?> currentFuture = CompletableFuture.completedFuture(null);
            runningTasks.put(taskId, currentFuture);
            
            try {
                // Mark task as started
                task = taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING);
                progressService.markTaskStarted(taskId);
                
                // Perform the conversion
                ConversionResult result = performConversion(task);
                
                // Store the result
                FileStorageService.StoredFile resultFile = fileStorageService.storeResultFile(
                    taskId, result.getContent(), result.getFileName()
                );
                
                // Mark task as completed
                task = taskService.markTaskCompleted(taskId, resultFile.getFilePath(), resultFile.getFileSize());
                progressService.markTaskCompleted(taskId);
                
                logger.info("Completed conversion for task {}", taskId);
                return CompletableFuture.completedFuture(task);
                
            } catch (Exception e) {
                logger.error("Conversion failed for task {}: {}", taskId, e.getMessage(), e);
                
                // Mark task as failed
                task = taskService.markTaskFailed(taskId, e.getMessage());
                progressService.markTaskFailed(taskId, e.getMessage());
                
                throw new ConversionException("Conversion failed: " + e.getMessage(), e);
            } finally {
                // Remove from running tasks
                runningTasks.remove(taskId);
            }
            
        } catch (Exception e) {
            logger.error("Failed to start conversion for task {}: {}", taskId, e.getMessage(), e);
            throw new ConversionException("Failed to start conversion", e);
        }
    }

    /**
     * Cancel a running conversion task
     */
    public boolean cancelConversion(String taskId) {
        Future<?> runningTask = runningTasks.get(taskId);
        
        if (runningTask != null && !runningTask.isDone()) {
            boolean cancelled = runningTask.cancel(true);
            
            if (cancelled) {
                runningTasks.remove(taskId);
                taskService.cancelTask(taskId);
                progressService.markTaskCancelled(taskId);
                
                logger.info("Cancelled conversion for task {}", taskId);
                return true;
            }
        }
        
        return false;
    }
    /**
     * Cancel a running conversion task
     */
    public boolean deleteConversion(String taskId) {
        Future<?> runningTask = runningTasks.get(taskId);
        
        if (runningTask != null && !runningTask.isDone()) {
            boolean cancelled = runningTask.cancel(true);
            
            if (cancelled) {
                runningTasks.remove(taskId);
                //FIXME 真的删除任务？
                fileStorageService.deleteTaskFiles(taskId); 
                taskService.deleteTask(taskId);
                progressService.markTaskCancelled(taskId);
                
                logger.info("Cancelled conversion for task {}", taskId);
                return true;
            }
        }else {
              //FIXME 真的删除任务？
                fileStorageService.deleteTaskFiles(taskId); 
                taskService.deleteTask(taskId);
                progressService.markTaskCancelled(taskId);
                logger.info("Deleted conversion for task {}", taskId);
                return true;
        }
        
        return false;
    }

    /**
     * Check if a task is currently running
     */
    public boolean isTaskRunning(String taskId) {
        Future<?> runningTask = runningTasks.get(taskId);
        return runningTask != null && !runningTask.isDone();
    }

    /**
     * Get the number of currently running tasks
     */
    public int getRunningTaskCount() {
        // Clean up completed tasks
        runningTasks.entrySet().removeIf(entry -> entry.getValue().isDone());
        return runningTasks.size();
    }

    /**
     * Perform the actual document conversion
     */
    private ConversionResult performConversion(ConversionTask task) throws Exception {
        String filePath = task.getFilePath();
        String fileType = task.getFileType();
        String taskId = task.getTaskId();

        logger.debug("Converting file {} of type {} for task {}", filePath, fileType, taskId);

        // Update progress
        progressService.updateProgress(taskId, TaskStatus.PROCESSING, 10, "Initializing conversion", "Preparing");

        // Create document processor chain
        DocumentProcessor processor = createDocumentProcessorChain();
        if (processor == null) {
            throw new ConversionException("No document processor plugins found. Please install at least one document processor plugin.");
        }

        progressService.updateProgress(taskId, TaskStatus.PROCESSING, 20, "Found processor for: " + fileType, "Processing");

        try {
            // Read input file
            Path inputPath = Paths.get(filePath);
            if (!Files.exists(inputPath)) {
                throw new ConversionException("Input file not found: " + filePath);
            }

            progressService.updateProgress(taskId, TaskStatus.PROCESSING, 30, "Reading input file", "Reading");

            // Perform conversion using document processor
            ProcessingResult result = convertFileWithProcessor(processor, inputPath, taskId);

            progressService.updateProgress(taskId, TaskStatus.PROCESSING, 90, "Conversion completed", "Finalizing");

            // Generate result file name
            String resultFileName = task.getTargetFileName();
            if (resultFileName == null) {
                resultFileName = generateResultFileName(task.getFileName());
            }

            progressService.updateProgress(taskId, TaskStatus.PROCESSING, 95, "Preparing result", "Saving");

            // Read the converted content from the output file
            String markdownContent = "";
            if (result.isSuccess() && result.getOutputFile() != null && result.getOutputFile().exists()) {
                markdownContent = Files.readString(result.getOutputFile().toPath());
            } else {
                throw new ConversionException("Conversion failed: " + result.getErrorMessage());
            }

            return new ConversionResult(resultFileName, markdownContent);

        } catch (Exception e) {
            logger.error("Error during conversion for task {}: {}", taskId, e.getMessage());
            throw new ConversionException("Conversion error: " + e.getMessage(), e);
        }
    }

    /**
     * Create document processor chain similar to ConvertCommand
     */
    private DocumentProcessor createDocumentProcessorChain() {
        List<DocumentProcessor> processors = pluginManager.getPlugins().stream()
                .filter(p -> p instanceof DocumentProcessor)
                .map(p -> (DocumentProcessor) p)
                .collect(Collectors.toList());

        if (processors.isEmpty()) {
            return null;
        }

        return new DocumentProcessingChain(processors);
    }

    /**
     * Convert file using document processor with progress tracking
     */
    private ProcessingResult convertFileWithProcessor(DocumentProcessor processor, Path inputPath, String taskId) throws Exception {
        progressService.updateProgress(taskId, TaskStatus.PROCESSING, 40, "Starting document processing", "Converting");

        try {
            File inputFile = inputPath.toFile();
            String ext = FilenameUtils.getExtension(inputFile.getName());

            // Check if file is supported
            if (!processor.supports(ext)) {
                throw new ConversionException("Unsupported file type: " + ext);
            }

            // Create temporary output directory for this task
            Path tempOutputDir = Files.createTempDirectory("conversion_" + taskId);

            // Build processing context similar to ConvertCommand
            ProcessingContext context = new ProcessingContext.Builder()
                .setOutputPath(tempOutputDir)
                .setForce(true)
                .build();

            progressService.updateProgress(taskId, TaskStatus.PROCESSING, 50, "Processing document...", "Converting");

            // Process the file
            ProcessingResult result = processor.process(inputFile, context);

            // Update progress based on result
            if (result.isSuccess()) {
                progressService.updateProgress(taskId, TaskStatus.PROCESSING, 85, "Document processed successfully", "Post-processing");
            } else {
                logger.error("Processing failed for task {}: {}", taskId, result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            logger.error("Error processing document for task {}: {}", taskId, e.getMessage());
            throw new ConversionException("Document processing failed", e);
        }
    }

    /**
     * Generate result file name
     */
    private String generateResultFileName(String originalFileName) {
        String nameWithoutExtension = originalFileName.replaceFirst("[.][^.]+$", "");
        return nameWithoutExtension + ".md";
    }

    /**
     * Result of a conversion operation
     */
    private static class ConversionResult {
        private final String fileName;
        private final String content;

        public ConversionResult(String fileName, String content) {
            this.fileName = fileName;
            this.content = content;
        }

        public String getFileName() { return fileName; }
        public String getContent() { return content; }
    }
}
