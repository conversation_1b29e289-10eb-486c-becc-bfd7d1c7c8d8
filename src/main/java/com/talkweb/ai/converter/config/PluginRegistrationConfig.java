package com.talkweb.ai.converter.config;

import com.talkweb.ai.converter.core.Plugin;
import com.talkweb.ai.converter.core.PluginException;
import com.talkweb.ai.converter.core.PluginManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

import java.util.Map;

/**
 * 插件注册配置类
 * 在Spring应用启动完成后自动注册所有Plugin类型的Bean到插件管理器
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class PluginRegistrationConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(PluginRegistrationConfig.class);
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    private PluginManager pluginManager;
    
    /**
     * 在Spring应用启动完成后注册所有Plugin Bean
     */
    @PostConstruct
    public void registerPluginBeans() {
        logger.info("Starting to register Plugin beans to PluginManager...");
        
        try {
            // 获取所有Plugin类型的Bean
            Map<String, Plugin> pluginBeans = applicationContext.getBeansOfType(Plugin.class);
            
            if (pluginBeans.isEmpty()) {
                logger.warn("No Plugin beans found in Spring context");
                return;
            }
            
            logger.info("Found {} Plugin beans to register", pluginBeans.size());
            
            // 注册每个插件Bean
            int successCount = 0;
            int failureCount = 0;
            
            for (Map.Entry<String, Plugin> entry : pluginBeans.entrySet()) {
                String beanName = entry.getKey();
                Plugin plugin = entry.getValue();
                
                try {
                    pluginManager.registerPlugin(plugin);
                    logger.info("Successfully registered plugin bean: {} (ID: {})", 
                              beanName, plugin.getMetadata().getId());
                    successCount++;
                } catch (PluginException e) {
                    logger.error("Failed to register plugin bean: {} (ID: {}), error: {}", 
                               beanName, plugin.getMetadata().getId(), e.getMessage());
                    failureCount++;
                }
            }
            
            logger.info("Plugin bean registration completed: {} successful, {} failed", 
                       successCount, failureCount);
            
            // 初始化和启动已注册的插件
            if (successCount > 0) {
                try {
                    pluginManager.initPlugins();
                    pluginManager.startPlugins();
                    logger.info("All registered plugins have been initialized and started");
                } catch (PluginException e) {
                    logger.error("Failed to initialize or start plugins: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("Error during plugin bean registration: {}", e.getMessage(), e);
        }
    }
}
