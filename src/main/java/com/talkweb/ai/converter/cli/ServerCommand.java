package com.talkweb.ai.converter.cli;

import com.talkweb.ai.converter.cli.util.ConsoleColors;
import com.talkweb.ai.converter.cli.util.ConsoleLogger;
import org.springframework.stereotype.Component;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.ParentCommand;

import java.util.concurrent.Callable;

@Component
@Command(
    name = "server",
    description = "Run the application as a web server with REST API and Web UI",
    mixinStandardHelpOptions = true
)
public class ServerCommand implements Callable<Integer> {

    @ParentCommand
    private DocConverterCommand parent;

    @Option(
        names = {"-p", "--port"},
        description = "Server port (default: ${DEFAULT-VALUE})",
        defaultValue = "8080"
    )
    private int port;

    @Option(
        names = {"--context-path"},
        description = "Server context path (default: ${DEFAULT-VALUE})",
        defaultValue = "/doc-converter"
    )
    private String contextPath;

    @Option(
        names = {"--max-file-size"},
        description = "Maximum file upload size in MB (default: ${DEFAULT-VALUE})",
        defaultValue = "100"
    )
    private int maxFileSize;

    @Option(
        names = {"--max-concurrent-tasks"},
        description = "Maximum concurrent conversion tasks (default: ${DEFAULT-VALUE})",
        defaultValue = "10"
    )
    private int maxConcurrentTasks;

    private final ConsoleLogger logger = new ConsoleLogger();

    @Override
    public Integer call() throws Exception {
        logger.success("Document Converter Web Server started successfully!");
        logger.info("Server URL: %shttp://localhost:%d%s%s",
            ConsoleColors.CYAN, port, contextPath, ConsoleColors.RESET);
        logger.info("API Documentation: %shttp://localhost:%d%s/swagger-ui.html%s",
            ConsoleColors.CYAN, port, contextPath, ConsoleColors.RESET);
        logger.info("Press Ctrl+C to stop the server");

        try {
            // Keep the server running
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            logger.info("Server interrupted, shutting down...");
            Thread.currentThread().interrupt();
        }

        return 0;
    }
}
