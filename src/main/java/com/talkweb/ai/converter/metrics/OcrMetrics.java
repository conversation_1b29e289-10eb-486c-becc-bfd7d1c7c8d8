package com.talkweb.ai.converter.metrics;

import io.micrometer.core.instrument.*;
import io.micrometer.core.instrument.binder.MeterBinder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;

/**
 * OCR性能指标收集组件
 * 
 * 提供OCR处理过程中的各种性能指标收集功能，包括：
 * - 处理时间统计
 * - 成功率和错误率监控
 * - 吞吐量统计
 * - 图像类型和尺寸分析
 * - 缓存命中率统计
 * - 线程池使用情况
 * 
 * 支持Prometheus、Grafana等监控系统集成
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class OcrMetrics implements MeterBinder {
    
    private static final Logger logger = LoggerFactory.getLogger(OcrMetrics.class);
    
    // 指标名称常量
    private static final String METRIC_PREFIX = "ocr";
    private static final String PROCESSING_TIME = METRIC_PREFIX + ".processing.time";
    private static final String PROCESSING_COUNT = METRIC_PREFIX + ".processing.count";
    private static final String SUCCESS_COUNT = METRIC_PREFIX + ".success.count";
    private static final String ERROR_COUNT = METRIC_PREFIX + ".error.count";
    private static final String CACHE_HIT_COUNT = METRIC_PREFIX + ".cache.hit.count";
    private static final String CACHE_MISS_COUNT = METRIC_PREFIX + ".cache.miss.count";
    private static final String THREAD_POOL_ACTIVE = METRIC_PREFIX + ".threadpool.active";
    private static final String THREAD_POOL_QUEUE_SIZE = METRIC_PREFIX + ".threadpool.queue.size";
    private static final String IMAGE_SIZE_HISTOGRAM = METRIC_PREFIX + ".image.size";
    private static final String CONFIDENCE_HISTOGRAM = METRIC_PREFIX + ".confidence";
    
    // Micrometer指标对象
    private Timer processingTimer;
    private Counter totalProcessingCounter;
    private Counter successCounter;
    private Counter errorCounter;
    private Counter cacheHitCounter;
    private Counter cacheMissCounter;
    private Gauge activeThreadsGauge;
    private Gauge queueSizeGauge;
    private DistributionSummary imageSizeDistribution;
    private DistributionSummary confidenceDistribution;
    
    // 内部统计数据
    private final AtomicLong activeThreads = new AtomicLong(0);
    private final AtomicLong queueSize = new AtomicLong(0);
    private final LongAdder totalProcessed = new LongAdder();
    private final LongAdder totalSuccessful = new LongAdder();
    private final LongAdder totalErrors = new LongAdder();
    private final LongAdder totalCacheHits = new LongAdder();
    private final LongAdder totalCacheMisses = new LongAdder();
    
    // 按错误类型统计
    private final Map<String, Counter> errorCountersByType = new ConcurrentHashMap<>();
    
    // 按图像类型统计
    private final Map<String, Timer> processingTimersByImageType = new ConcurrentHashMap<>();
    
    private MeterRegistry meterRegistry;
    
    @PostConstruct
    public void init() {
        logger.info("OCR metrics component initialized");
    }
    
    @Override
    public void bindTo(MeterRegistry registry) {
        this.meterRegistry = registry;
        
        // 处理时间计时器
        processingTimer = Timer.builder(PROCESSING_TIME)
            .description("OCR processing time")
            .tag("component", "ocr-service")
            .register(registry);
        
        // 处理总数计数器
        totalProcessingCounter = Counter.builder(PROCESSING_COUNT)
            .description("Total OCR processing count")
            .tag("component", "ocr-service")
            .register(registry);
        
        // 成功计数器
        successCounter = Counter.builder(SUCCESS_COUNT)
            .description("Successful OCR processing count")
            .tag("component", "ocr-service")
            .register(registry);
        
        // 错误计数器
        errorCounter = Counter.builder(ERROR_COUNT)
            .description("Failed OCR processing count")
            .tag("component", "ocr-service")
            .register(registry);
        
        // 缓存命中计数器
        cacheHitCounter = Counter.builder(CACHE_HIT_COUNT)
            .description("OCR cache hit count")
            .tag("component", "ocr-cache")
            .register(registry);
        
        // 缓存未命中计数器
        cacheMissCounter = Counter.builder(CACHE_MISS_COUNT)
            .description("OCR cache miss count")
            .tag("component", "ocr-cache")
            .register(registry);
        
        // 活跃线程数量表
        activeThreadsGauge = Gauge.builder(THREAD_POOL_ACTIVE, this, metrics -> metrics.activeThreads.get())
            .description("Active threads in OCR thread pool")
            .tag("component", "ocr-threadpool")
            .register(registry);

        // 队列大小表
        queueSizeGauge = Gauge.builder(THREAD_POOL_QUEUE_SIZE, this, metrics -> metrics.queueSize.get())
            .description("Queue size of OCR thread pool")
            .tag("component", "ocr-threadpool")
            .register(registry);
        
        // 图像尺寸分布
        imageSizeDistribution = DistributionSummary.builder(IMAGE_SIZE_HISTOGRAM)
            .description("Distribution of processed image sizes")
            .tag("component", "ocr-service")
            .register(registry);
        
        // 置信度分布
        confidenceDistribution = DistributionSummary.builder(CONFIDENCE_HISTOGRAM)
            .description("Distribution of OCR confidence scores")
            .tag("component", "ocr-service")
            .register(registry);
        
        logger.info("OCR metrics bound to meter registry: {}", registry.getClass().getSimpleName());
    }
    
    /**
     * 记录OCR处理开始
     */
    public Timer.Sample startProcessing() {
        totalProcessingCounter.increment();
        totalProcessed.increment();
        return Timer.start(meterRegistry);
    }
    
    /**
     * 记录OCR处理完成
     */
    public void recordProcessingSuccess(Timer.Sample sample, double confidence, int imageSize) {
        sample.stop(processingTimer);
        successCounter.increment();
        totalSuccessful.increment();
        
        // 记录置信度和图像尺寸
        confidenceDistribution.record(confidence);
        imageSizeDistribution.record(imageSize);
    }
    
    /**
     * 记录OCR处理成功（带图像类型）
     */
    public void recordProcessingSuccess(Timer.Sample sample, double confidence, int imageSize, String imageType) {
        recordProcessingSuccess(sample, confidence, imageSize);
        
        // 按图像类型记录处理时间
        Timer typeTimer = processingTimersByImageType.computeIfAbsent(imageType, 
            type -> Timer.builder(PROCESSING_TIME)
                .description("OCR processing time by image type")
                .tag("component", "ocr-service")
                .tag("image_type", type)
                .register(meterRegistry));
        
        sample.stop(typeTimer);
    }
    
    /**
     * 记录OCR处理失败
     */
    public void recordProcessingError(Timer.Sample sample, String errorType) {
        sample.stop(processingTimer);
        errorCounter.increment();
        totalErrors.increment();
        
        // 按错误类型统计
        Counter typeCounter = errorCountersByType.computeIfAbsent(errorType,
            type -> Counter.builder(ERROR_COUNT)
                .description("Failed OCR processing count by error type")
                .tag("component", "ocr-service")
                .tag("error_type", type)
                .register(meterRegistry));
        
        typeCounter.increment();
    }
    
    /**
     * 记录缓存命中
     */
    public void recordCacheHit() {
        cacheHitCounter.increment();
        totalCacheHits.increment();
    }
    
    /**
     * 记录缓存未命中
     */
    public void recordCacheMiss() {
        cacheMissCounter.increment();
        totalCacheMisses.increment();
    }
    
    /**
     * 更新线程池状态
     */
    public void updateThreadPoolStats(long activeThreads, long queueSize) {
        this.activeThreads.set(activeThreads);
        this.queueSize.set(queueSize);
    }
    
    /**
     * 记录批处理指标
     */
    public void recordBatchProcessing(int totalItems, int successfulItems, int failedItems, long processingTimeMs) {
        // 批处理成功率
        double successRate = totalItems > 0 ? (double) successfulItems / totalItems : 0.0;
        Gauge.builder(METRIC_PREFIX + ".batch.success.rate", () -> successRate)
            .description("Batch processing success rate")
            .tag("component", "batch-processor")
            .register(meterRegistry);

        // 批处理吞吐量
        double throughput = processingTimeMs > 0 ? (double) totalItems * 1000 / processingTimeMs : 0.0;
        Gauge.builder(METRIC_PREFIX + ".batch.throughput", () -> throughput)
            .description("Batch processing throughput (items per second)")
            .tag("component", "batch-processor")
            .register(meterRegistry);
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        long total = totalProcessed.sum();
        return total > 0 ? (double) totalSuccessful.sum() / total : 0.0;
    }
    
    /**
     * 获取错误率
     */
    public double getErrorRate() {
        long total = totalProcessed.sum();
        return total > 0 ? (double) totalErrors.sum() / total : 0.0;
    }
    
    /**
     * 获取缓存命中率
     */
    public double getCacheHitRate() {
        long totalCacheRequests = totalCacheHits.sum() + totalCacheMisses.sum();
        return totalCacheRequests > 0 ? (double) totalCacheHits.sum() / totalCacheRequests : 0.0;
    }
    
    /**
     * 获取吞吐量（每秒处理数）
     */
    public double getThroughput() {
        // 基于最近的处理时间计算吞吐量
        if (processingTimer == null) {
            return 0.0;
        }
        return processingTimer.count() > 0 ?
            processingTimer.count() / processingTimer.totalTime(java.util.concurrent.TimeUnit.SECONDS) : 0.0;
    }
    
    /**
     * 获取指标摘要
     */
    public MetricsSummary getMetricsSummary() {
        double avgProcessingTime = (processingTimer != null) ?
            processingTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS) : 0.0;

        return new MetricsSummary(
            totalProcessed.sum(),
            totalSuccessful.sum(),
            totalErrors.sum(),
            getSuccessRate(),
            getErrorRate(),
            getCacheHitRate(),
            getThroughput(),
            avgProcessingTime,
            activeThreads.get(),
            queueSize.get()
        );
    }
    
    /**
     * 指标摘要类
     */
    public static class MetricsSummary {
        private final long totalProcessed;
        private final long totalSuccessful;
        private final long totalErrors;
        private final double successRate;
        private final double errorRate;
        private final double cacheHitRate;
        private final double throughput;
        private final double avgProcessingTimeMs;
        private final long activeThreads;
        private final long queueSize;
        
        public MetricsSummary(long totalProcessed, long totalSuccessful, long totalErrors,
                            double successRate, double errorRate, double cacheHitRate,
                            double throughput, double avgProcessingTimeMs,
                            long activeThreads, long queueSize) {
            this.totalProcessed = totalProcessed;
            this.totalSuccessful = totalSuccessful;
            this.totalErrors = totalErrors;
            this.successRate = successRate;
            this.errorRate = errorRate;
            this.cacheHitRate = cacheHitRate;
            this.throughput = throughput;
            this.avgProcessingTimeMs = avgProcessingTimeMs;
            this.activeThreads = activeThreads;
            this.queueSize = queueSize;
        }
        
        // Getters
        public long getTotalProcessed() { return totalProcessed; }
        public long getTotalSuccessful() { return totalSuccessful; }
        public long getTotalErrors() { return totalErrors; }
        public double getSuccessRate() { return successRate; }
        public double getErrorRate() { return errorRate; }
        public double getCacheHitRate() { return cacheHitRate; }
        public double getThroughput() { return throughput; }
        public double getAvgProcessingTimeMs() { return avgProcessingTimeMs; }
        public long getActiveThreads() { return activeThreads; }
        public long getQueueSize() { return queueSize; }
        
        @Override
        public String toString() {
            return String.format("MetricsSummary{processed=%d, successful=%d, errors=%d, " +
                               "successRate=%.2f%%, errorRate=%.2f%%, cacheHitRate=%.2f%%, " +
                               "throughput=%.2f/s, avgTime=%.2fms, activeThreads=%d, queueSize=%d}",
                               totalProcessed, totalSuccessful, totalErrors,
                               successRate * 100, errorRate * 100, cacheHitRate * 100,
                               throughput, avgProcessingTimeMs, activeThreads, queueSize);
        }
    }
}
