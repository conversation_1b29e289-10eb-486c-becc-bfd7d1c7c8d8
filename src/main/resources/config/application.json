{"doc-converter": {"app": {"name": "Document Converter", "version": "1.0.0", "max-concurrent-tasks": 4, "temp-dir": "${TEMP:/tmp/doc-converter}"}, "file": {"supported-formats": ["pdf", "docx", "doc", "xlsx", "pptx", "html", "htm", "txt", "md"], "default-encoding": "UTF-8", "max-file-size": 104857600, "skip-unsupported": true}, "output": {"directory": "output", "preserve-structure": true, "filename-pattern": "{name}.md", "overwrite-existing": false}, "logging": {"level": "INFO", "file": "logs/application.log", "max-size": "10MB", "max-backups": 5, "pattern": "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"}, "plugins": {"directory": "plugins", "auto-scan": true, "enabled-plugins": ["pdf-converter", "docx-converter", "excel-converter", "pptx-converter", "html-converter", "txt-converter"], "config": {"pdf-converter": {"extract-images": true, "image-quality": 0.8, "ocr-enabled": true, "ocr-language": "eng+chi_sim"}, "docx-converter": {"extract-comments": true, "extract-track-changes": false}, "excel-converter": {"include-sheets": "*", "include-hidden-sheets": false, "include-formulas": true}, "pptx-converter": {"extract-speaker-notes": true, "extract-slide-notes": true, "extract-slide-titles": true}, "html-converter": {"extract-links": true, "extract-images": true, "base-href": ""}}}, "performance": {"thread-pool": {"core-size": 4, "max-size": 8, "queue-capacity": 100, "keep-alive-seconds": 60}, "memory": {"max-memory-usage": 0.8, "check-interval": 5000}}, "security": {"validate-file-paths": true, "prevent-directory-traversal": true, "allowed-protocols": ["file"], "max-files-per-operation": 1000}}, "spring": {"application": {"name": "doc-converter"}, "management": {"endpoints": {"web": {"exposure": {"include": ["health", "info", "metrics"]}}, "health": {"show-details": "when_authorized"}, "metrics": {"enabled": true}}, "health": {"defaults": {"enabled": true}, "diskspace": {"enabled": true}, "diskSpace": {"path": ".", "threshold": "10MB"}}}}, "logging": {"level": {"root": "INFO", "com.talkweb.ai.indexer": "DEBUG"}, "file": {"name": "${doc-converter.logging.file:logs/application.log}", "max-size": "${doc-converter.logging.max-size:10MB}"}, "pattern": {"console": "%clr(%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}", "file": "%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"}}}