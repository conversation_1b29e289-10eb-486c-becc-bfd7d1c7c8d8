# 用户指南

## 安装
1. 确保已安装 Java 21+
2. 下载最新版本：
```bash
curl -L https://download.talkweb.com/doc-converter/latest.jar -o doc-converter.jar
```

## 配置
编辑 `config/application.yaml`:
```yaml
server:
  port: 8080
spring:
  ai:
    ollama:
      base-url: http://localhost:11434
      chat:
        model: llama3
```

## 使用命令行
### 转换单个文件
```bash
java -jar doc-converter.jar convert -i input.docx -o output/
```

### 批量转换目录
```bash
java -jar doc-converter.jar convert -i docs/ -r --parallel
```

### 启用AI增强
```bash
java -jar doc-converter.jar convert -i input.pdf --ai --ai-model qwen3-14b
```

## Web界面
启动服务：
```bash
java -jar doc-converter.jar server
```
访问 http://localhost:8080

## 插件管理
列出插件：
```bash
java -jar doc-converter.jar plugin list
```

安装插件：
```bash
java -jar doc-converter.jar plugin install plugin.jar