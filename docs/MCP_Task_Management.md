# MCP 任务管理功能文档

## 概述

本文档介绍如何通过 MCP (Model Context Protocol) SSE (Server-Sent Events) 方式暴露和使用任务管理的增删改查功能。

## 功能特性

### 🚀 核心功能
- ✅ **任务列表查询** - 支持分页和状态过滤
- ✅ **任务详情获取** - 根据ID获取特定任务信息
- ✅ **任务创建** - 创建新的文档转换任务
- ✅ **任务更新** - 更新任务优先级和选项
- ✅ **任务删除** - 删除指定任务
- ✅ **任务状态查询** - 获取任务执行状态和进度
- ✅ **任务取消** - 取消正在运行的任务
- ✅ **任务重试** - 重试失败的任务

### 🔧 技术特性
- ✅ **SSE 传输** - 使用 Server-Sent Events 而非 STDIO
- ✅ **实时更新** - 支持实时事件流
- ✅ **错误处理** - 完善的错误处理和日志记录
- ✅ **类型安全** - 强类型参数验证
- ✅ **异步处理** - 基于 Reactor 的响应式编程

## 配置说明

### 1. 依赖配置

在 `pom.xml` 中添加 MCP 依赖：

```xml
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-mcp</artifactId>
</dependency>

<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-starter-mcp-server</artifactId>
</dependency>
```

### 2. 应用配置

在 `application-server.yaml` 中配置：

```yaml
spring:
  ai:
    mcp:
      server:
        enabled: true
        transport:
          stdio:
            enabled: false  # 禁用 STDIO 传输
          sse:
            enabled: true
            endpoint: /mcp/tasks
            port: 8081
            cors-allowed-origins: "*"
            heartbeat-interval: 30000
```

## API 接口

### MCP 工具列表

| 工具名称 | 描述 | 参数 |
|---------|------|------|
| `list_tasks` | 列出转换任务 | `page`, `size`, `status` |
| `get_task` | 获取特定任务 | `taskId` |
| `create_task` | 创建新任务 | `fileName`, `sourceFormat`, `targetFormat`, `priority`, `options` |
| `update_task` | 更新任务 | `taskId`, `priority`, `options` |
| `delete_task` | 删除任务 | `taskId` |
| `get_task_status` | 获取任务状态 | `taskId` |
| `cancel_task` | 取消任务 | `taskId` |
| `retry_task` | 重试任务 | `taskId` |

### REST API 演示接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/mcp/demo` | 运行 MCP 客户端演示 |
| GET | `/api/mcp/tasks` | 通过 MCP 列出任务 |
| GET | `/api/mcp/tasks/{taskId}` | 通过 MCP 获取任务 |
| POST | `/api/mcp/tasks` | 通过 MCP 创建任务 |
| PUT | `/api/mcp/tasks/{taskId}` | 通过 MCP 更新任务 |
| DELETE | `/api/mcp/tasks/{taskId}` | 通过 MCP 删除任务 |
| GET | `/api/mcp/tasks/{taskId}/status` | 通过 MCP 获取任务状态 |
| POST | `/api/mcp/tasks/{taskId}/cancel` | 通过 MCP 取消任务 |
| POST | `/api/mcp/tasks/{taskId}/retry` | 通过 MCP 重试任务 |
| GET | `/api/mcp/events` | MCP SSE 事件流 |

## 使用示例

### 1. 创建任务

```bash
curl -X POST http://localhost:8080/doc-converter/api/mcp/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "fileName": "document.pdf",
    "sourceFormat": "PDF",
    "targetFormat": "MARKDOWN",
    "priority": 1,
    "options": {
      "quality": "high",
      "preserveFormatting": true
    }
  }'
```

### 2. 列出任务

```bash
curl "http://localhost:8080/doc-converter/api/mcp/tasks?page=0&size=10&status=PENDING"
```

### 3. 获取任务状态

```bash
curl "http://localhost:8080/doc-converter/api/mcp/tasks/{taskId}/status"
```

### 4. 监听实时事件

```bash
curl -N "http://localhost:8080/doc-converter/api/mcp/events"
```

## MCP 客户端使用

### Java 客户端示例

```java
@Autowired
private McpTaskClient mcpTaskClient;

// 列出任务
mcpTaskClient.listTasks(0, 10, "PENDING")
    .subscribe(result -> {
        System.out.println("Tasks: " + result);
    });

// 创建任务
Map<String, Object> options = Map.of(
    "quality", "high",
    "preserveFormatting", true
);

mcpTaskClient.createTask("example.pdf", "PDF", "MARKDOWN", 1, options)
    .subscribe(result -> {
        System.out.println("Created task: " + result);
    });

// 监听实时事件
mcpTaskClient.connectToMcpServer()
    .subscribe(event -> {
        System.out.println("Received event: " + event);
    });
```

### JavaScript 客户端示例

```javascript
// 连接到 SSE 端点
const eventSource = new EventSource('http://localhost:8081/mcp/tasks');

eventSource.onmessage = function(event) {
    console.log('Received MCP event:', event.data);
};

// 发送工具调用请求
async function callMcpTool(toolName, parameters) {
    const response = await fetch('http://localhost:8081/mcp/tasks', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            method: 'tools/call',
            params: {
                name: toolName,
                arguments: parameters
            }
        })
    });
    
    return await response.json();
}

// 列出任务
const tasks = await callMcpTool('list_tasks', {
    page: 0,
    size: 10,
    status: 'PENDING'
});
```

## 错误处理

所有 MCP 工具调用都包含错误处理，返回格式如下：

### 成功响应
```json
{
    "task": { ... },
    "message": "Operation completed successfully"
}
```

### 错误响应
```json
{
    "error": "Error message describing what went wrong"
}
```

## 监控和日志

### 日志级别
- `INFO` - 重要操作和状态变化
- `DEBUG` - 详细的调试信息
- `ERROR` - 错误和异常

### 关键日志
- MCP 服务器启动和配置
- 工具调用和响应
- SSE 连接状态
- 错误和异常处理

## 性能考虑

### 连接管理
- SSE 连接自动重连（最多3次）
- 连接超时设置为5分钟
- 心跳间隔30秒

### 资源优化
- 分页查询避免大量数据传输
- 异步处理提高响应性能
- 连接池管理减少资源消耗

## 安全考虑

### CORS 配置
- 默认允许所有来源（开发环境）
- 生产环境应限制特定域名

### 认证授权
- 当前版本未实现认证
- 建议在生产环境中添加 JWT 或其他认证机制

## 故障排除

### 常见问题

1. **STDIO 传输错误**
   - 确保 `spring.ai.mcp.server.transport.stdio.enabled=false`
   - 检查配置文件中的 SSE 设置

2. **连接失败**
   - 验证端口 8081 是否可用
   - 检查防火墙设置

3. **工具调用失败**
   - 检查参数格式和类型
   - 查看服务器日志获取详细错误信息

### 调试步骤

1. 启用 DEBUG 日志级别
2. 检查 MCP 服务器启动日志
3. 验证 SSE 端点可访问性
4. 测试简单的工具调用

## 扩展开发

### 添加新工具

1. 在 `TaskManagementMcpServer` 中添加新方法
2. 在 `McpTaskClient` 中添加对应的客户端方法
3. 更新 REST 控制器（如需要）
4. 添加相应的测试用例

### 自定义传输

可以扩展现有的 SSE 传输或实现自定义传输协议：

```java
@Bean
public ServerTransport customMcpTransport() {
    return CustomServerTransport.builder()
        .endpoint("/custom/mcp")
        .port(8082)
        .build();
}
```
