# 快速入门

## 转换单个文档
```bash
java -jar doc-converter.jar convert -i presentation.pptx -o ./markdown-output
```

## 批量转换文档
```bash
java -jar doc-converter.jar convert -i ./documents -r --parallel
```

## 使用AI增强
```bash
java -jar doc-converter.jar convert -i report.pdf --ai --ai-model qwen3-14b
```

## 启动Web服务
```bash
java -jar doc-converter.jar server
```
访问 http://localhost:8080 上传文件

## 常用选项
- `-v` 详细输出
- `--force` 覆盖已有文件
- `--threads 8` 设置并发线程数
- `--no-color` 禁用彩色输出

## 插件管理示例
```bash
# 列出已安装插件
java -jar doc-converter.jar plugin list

# 安装新插件
java -jar doc-converter.jar plugin install ./plugins/ocr-plugin.jar