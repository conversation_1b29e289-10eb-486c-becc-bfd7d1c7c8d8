#!/bin/bash

# Document Converter 使用示例脚本
# 演示各种常见的使用场景

set -e

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[示例]${NC} $1"
}

echo_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

echo_note() {
    echo -e "${YELLOW}[注意]${NC} $1"
}

echo ""
echo "Document Converter 使用示例"
echo "=========================="
echo ""

# 示例1：启动Web服务器
echo_info "示例1：启动Web服务器"
echo_step "执行命令: ./start.sh"
echo_note "服务器将在 http://localhost:8080 启动"
echo_note "API文档: http://localhost:8080/swagger-ui.html"
echo ""

# 示例2：转换单个文件
echo_info "示例2：转换单个PDF文件"
echo_step "执行命令: ./start.sh convert document.pdf"
echo_note "输出文件将保存在 output/document.md"
echo ""

# 示例3：转换目录
echo_info "示例3：转换整个目录"
echo_step "执行命令: ./start.sh convert /path/to/documents -o /path/to/output"
echo_note "递归处理目录中的所有支持格式文件"
echo ""

# 示例4：并行处理
echo_info "示例4：并行处理提高效率"
echo_step "执行命令: ./start.sh convert /path/to/documents --parallel --threads 8"
echo_note "使用8个线程并行处理文件"
echo ""

# 示例5：AI增强转换
echo_info "示例5：启用AI增强转换"
echo_step "执行命令: ./start.sh convert document.pdf --ai --ai-model qwen3-14b"
echo_note "使用AI模型增强转换质量"
echo ""

# 示例6：自定义输出格式
echo_info "示例6：自定义转换选项"
echo_step "执行命令: ./start.sh convert documents/ --include '*.pdf,*.docx' --exclude '*.tmp' --force"
echo_note "只处理PDF和DOCX文件，排除临时文件，强制覆盖现有输出"
echo ""

# 示例7：状态监控
echo_info "示例7：监控应用状态"
echo_step "检查简单状态: ./status.sh simple"
echo_step "查看详细状态: ./status.sh"
echo_step "健康检查: ./status.sh health"
echo_step "查看日志: ./status.sh logs"
echo ""

# 示例8：服务管理
echo_info "示例8：服务管理"
echo_step "启动服务: ./start.sh"
echo_step "正常停止: ./stop.sh"
echo_step "强制停止: ./stop.sh force"
echo_step "停止所有: ./stop.sh all"
echo ""

# 示例9：插件管理
echo_info "示例9：插件管理"
echo_step "列出插件: ./start.sh plugin list"
echo_step "安装插件: ./start.sh plugin install plugin-name"
echo_step "卸载插件: ./start.sh plugin uninstall plugin-name"
echo ""

# 示例10：配置管理
echo_info "示例10：自定义配置"
echo_step "指定配置文件: ./start.sh -c custom-config.yaml convert document.pdf"
echo_step "指定插件目录: ./start.sh --plugins-dir /custom/plugins convert document.pdf"
echo_step "指定临时目录: ./start.sh --temp-dir /custom/temp convert document.pdf"
echo ""

echo_info "完整的使用说明请参考 SCRIPTS_README.md 文件"
echo ""

# 交互式示例选择
if [ "$1" = "interactive" ]; then
    echo "选择要运行的示例："
    echo "1) 启动Web服务器"
    echo "2) 转换测试文件"
    echo "3) 查看应用状态"
    echo "4) 显示帮助信息"
    echo "5) 退出"
    echo ""
    
    read -p "请输入选项 (1-5): " choice
    
    case $choice in
        1)
            echo_step "启动Web服务器..."
            ./start.sh
            ;;
        2)
            if [ ! -f "test_input/test.txt" ]; then
                echo_step "创建测试文件..."
                mkdir -p test_input
                cat > test_input/test.txt << 'EOF'
# 测试文档

这是一个测试文档，用于验证Document Converter的转换功能。

## 功能特性

- 支持多种文档格式
- 高质量的Markdown转换
- AI增强处理
- 批量处理能力

感谢使用Document Converter！
EOF
            fi
            
            echo_step "转换测试文件..."
            ./start.sh convert test_input/test.txt -o test_output
            
            if [ -f "test_output/test.md" ]; then
                echo_step "转换成功！输出文件："
                cat test_output/test.md
            fi
            ;;
        3)
            echo_step "查看应用状态..."
            ./status.sh
            ;;
        4)
            echo_step "显示帮助信息..."
            ./start.sh --help
            ;;
        5)
            echo_step "退出"
            exit 0
            ;;
        *)
            echo_note "无效选项"
            exit 1
            ;;
    esac
fi
