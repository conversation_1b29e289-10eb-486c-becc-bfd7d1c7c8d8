#!/bin/bash

# Document Converter 启动脚本
# 用法:
#   ./start.sh                    # 启动server模式
#   ./start.sh convert <input>    # 转换模式，第一个参数为输入路径
#   ./start.sh [其他参数]         # 直接传递给应用程序

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_NAME="doc-converter"
JAR_FILE="target/${APP_NAME}-1.0.0-SNAPSHOT.jar"
PID_FILE="${SCRIPT_DIR}/${APP_NAME}.pid"
LOG_FILE="${SCRIPT_DIR}/logs/${APP_NAME}.log"
JVM_OPTS="-Xmx2g -Xms512m -XX:+UseG1GC"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        log_error "Java not found. Please install Java 21 or later."
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -lt 21 ]; then
        log_error "Java version $JAVA_VERSION is not supported. Please install Java 21 or later."
        exit 1
    fi
    
    log_debug "Java version: $JAVA_VERSION"
}

# 检查JAR文件
check_jar() {
    if [ ! -f "$JAR_FILE" ]; then
        log_warn "JAR file not found: $JAR_FILE"
        log_info "Building application..."
        
        if command -v mvn &> /dev/null; then
            mvn clean package -DskipTests
        else
            log_error "Maven not found. Please build the application manually or install Maven."
            exit 1
        fi
        
        if [ ! -f "$JAR_FILE" ]; then
            log_error "Failed to build JAR file: $JAR_FILE"
            exit 1
        fi
    fi
    
    log_debug "JAR file found: $JAR_FILE"
}

# 创建必要的目录
create_directories() {
    mkdir -p "$(dirname "$LOG_FILE")"
    mkdir -p "config"
    mkdir -p "plugins"
    mkdir -p "temp"
    mkdir -p "output"
}

# 启动server模式
start_server() {
    log_info "Starting $APP_NAME in server mode..."
    
    # 检查是否已经运行
    if [ -f "$PID_FILE" ] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
        log_warn "$APP_NAME is already running (PID: $(cat "$PID_FILE"))"
        return 0
    fi
    
    # 启动应用
    nohup java $JVM_OPTS -jar "$JAR_FILE" server > "$LOG_FILE" 2>&1 &
    echo $! > "$PID_FILE"
    
    # 等待启动
    sleep 3
    
    if kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
        log_info "$APP_NAME started successfully (PID: $(cat "$PID_FILE"))"
        log_info "Server will be available at: http://localhost:8080"
        log_info "API documentation: http://localhost:8080/swagger-ui.html"
        log_info "Log file: $LOG_FILE"
    else
        log_error "Failed to start $APP_NAME"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 转换模式
convert_mode() {
    local input_path="$1"
    shift # 移除第一个参数
    
    if [ -z "$input_path" ]; then
        log_error "Input path is required for convert mode"
        log_info "Usage: $0 convert <input_path> [other_options]"
        exit 1
    fi
    
    log_info "Starting conversion: $input_path"
    
    # 构建命令参数
    local args=("convert" "-i" "$input_path")
    
    # 添加其他参数
    while [ $# -gt 0 ]; do
        args+=("$1")
        shift
    done
    
    # 执行转换
    java $JVM_OPTS -jar "$JAR_FILE" "${args[@]}"
}

# 直接模式（传递所有参数）
direct_mode() {
    log_info "Running $APP_NAME with arguments: $*"
    java $JVM_OPTS -jar "$JAR_FILE" "$@"
}

# 主函数
main() {
    log_info "Document Converter Startup Script"
    log_info "================================="
    
    # 环境检查
    check_java
    check_jar
    create_directories
    
    # 根据参数决定运行模式
    if [ $# -eq 0 ]; then
        # 无参数，启动server模式
        start_server
    elif [ "$1" = "convert" ]; then
        # convert模式
        shift
        convert_mode "$@"
    else
        # 直接模式
        direct_mode "$@"
    fi
}

# 执行主函数
main "$@"
