#!/bin/bash

# Document Converter 安装脚本
# 自动设置环境和依赖

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_step "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        log_info "检测到 macOS 系统"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_info "检测到 Linux 系统"
    else
        log_warn "未知操作系统: $OSTYPE"
    fi
    
    # 检查Java
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
        if [ "$JAVA_VERSION" -ge 21 ]; then
            log_info "Java版本检查通过: $JAVA_VERSION"
        else
            log_error "需要Java 21或更高版本，当前版本: $JAVA_VERSION"
            return 1
        fi
    else
        log_error "未找到Java，请先安装Java 21或更高版本"
        return 1
    fi
    
    # 检查Maven
    if command -v mvn &> /dev/null; then
        MVN_VERSION=$(mvn -version | head -n1 | cut -d' ' -f3)
        log_info "Maven版本: $MVN_VERSION"
    else
        log_error "未找到Maven，请先安装Maven"
        return 1
    fi
    
    log_info "系统要求检查完成"
}

# 创建目录结构
create_directories() {
    log_step "创建目录结构..."
    
    local dirs=("logs" "config" "plugins" "temp" "output" "test_input" "test_output")
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        else
            log_info "目录已存在: $dir"
        fi
    done
}

# 设置脚本权限
set_permissions() {
    log_step "设置脚本执行权限..."
    
    local scripts=("start.sh" "stop.sh" "status.sh" "examples.sh" "install.sh")
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            chmod +x "$script"
            log_info "设置执行权限: $script"
        fi
    done
}

# 构建应用程序
build_application() {
    log_step "构建应用程序..."
    
    if [ -f "pom.xml" ]; then
        log_info "开始Maven构建..."
        mvn clean package -DskipTests
        
        if [ -f "target/doc-converter-1.0.0-SNAPSHOT.jar" ]; then
            log_info "构建成功: target/doc-converter-1.0.0-SNAPSHOT.jar"
        else
            log_error "构建失败，未找到JAR文件"
            return 1
        fi
    else
        log_error "未找到pom.xml文件"
        return 1
    fi
}

# 创建默认配置
create_default_config() {
    log_step "创建默认配置文件..."
    
    if [ ! -f "config/application.yaml" ]; then
        cat > config/application.yaml << 'EOF'
# Document Converter 配置文件

server:
  port: 8080
  servlet:
    context-path: /doc-converter

spring:
  application:
    name: doc-converter
  
  # 数据库配置
  datasource:
    url: jdbc:h2:mem:docconverter
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# 应用配置
app:
  plugins-dir: plugins
  temp-dir: temp
  max-concurrent-tasks: 10
  
# OCR配置
ocr:
  enabled: true
  languages: [eng, chi_sim]
  confidence-threshold: 50
  
# AI配置
ai:
  enabled: false
  model: qwen3-14b

# 日志配置
logging:
  level:
    com.talkweb.ai.converter: INFO
  file:
    name: logs/application.log
EOF
        log_info "创建默认配置: config/application.yaml"
    else
        log_info "配置文件已存在: config/application.yaml"
    fi
}

# 创建测试数据
create_test_data() {
    log_step "创建测试数据..."
    
    if [ ! -f "test_input/sample.txt" ]; then
        cat > test_input/sample.txt << 'EOF'
# Document Converter 测试文档

这是一个用于测试Document Converter功能的示例文档。

## 支持的格式

Document Converter支持以下文档格式的转换：

1. **Office文档**
   - Microsoft Word (.docx, .doc)
   - Microsoft Excel (.xlsx, .xls)
   - Microsoft PowerPoint (.pptx, .ppt)

2. **PDF文档**
   - Adobe PDF (.pdf)

3. **网页文档**
   - HTML (.html, .htm)

4. **文本文档**
   - 纯文本 (.txt)
   - Rich Text Format (.rtf)
   - OpenDocument Text (.odt)

5. **图像文档**
   - JPEG (.jpg, .jpeg)
   - PNG (.png)

## 主要特性

- **高质量转换**: 保持原文档的结构和格式
- **批量处理**: 支持目录递归处理
- **并行处理**: 多线程提高处理效率
- **AI增强**: 可选的AI内容优化
- **Web界面**: 友好的Web管理界面
- **REST API**: 完整的API接口
- **插件系统**: 可扩展的插件架构

## 使用方法

### 命令行模式
```bash
# 转换单个文件
./start.sh convert document.pdf

# 转换目录
./start.sh convert /path/to/documents

# 启用并行处理
./start.sh convert documents/ --parallel --threads 4
```

### Web服务模式
```bash
# 启动Web服务器
./start.sh

# 访问Web界面
# http://localhost:8080/doc-converter
```

感谢使用Document Converter！
EOF
        log_info "创建测试文件: test_input/sample.txt"
    else
        log_info "测试文件已存在: test_input/sample.txt"
    fi
}

# 验证安装
verify_installation() {
    log_step "验证安装..."
    
    # 检查JAR文件
    if [ -f "target/doc-converter-1.0.0-SNAPSHOT.jar" ]; then
        log_info "✓ JAR文件存在"
    else
        log_error "✗ JAR文件不存在"
        return 1
    fi
    
    # 检查脚本文件
    local scripts=("start.sh" "stop.sh" "status.sh")
    for script in "${scripts[@]}"; do
        if [ -x "$script" ]; then
            log_info "✓ $script 可执行"
        else
            log_error "✗ $script 不可执行"
            return 1
        fi
    done
    
    # 检查配置文件
    if [ -f "config/application.yaml" ]; then
        log_info "✓ 配置文件存在"
    else
        log_warn "✗ 配置文件不存在"
    fi
    
    log_info "安装验证完成"
}

# 显示安装后信息
show_post_install_info() {
    log_step "安装完成！"
    echo ""
    log_info "Document Converter 已成功安装"
    echo ""
    echo "快速开始："
    echo "  启动Web服务器:    ./start.sh"
    echo "  转换文档:        ./start.sh convert test_input/sample.txt"
    echo "  查看状态:        ./status.sh"
    echo "  查看示例:        ./examples.sh"
    echo "  停止服务:        ./stop.sh"
    echo ""
    echo "Web界面: http://localhost:8080/doc-converter"
    echo "API文档: http://localhost:8080/doc-converter/swagger-ui.html"
    echo ""
    echo "详细使用说明请参考: SCRIPTS_README.md"
    echo ""
}

# 主安装流程
main() {
    echo ""
    log_info "Document Converter 安装程序"
    log_info "============================"
    echo ""
    
    # 执行安装步骤
    check_requirements || exit 1
    create_directories
    set_permissions
    build_application || exit 1
    create_default_config
    create_test_data
    verify_installation || exit 1
    show_post_install_info
    
    echo ""
    log_info "安装完成！"
}

# 如果直接运行脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
